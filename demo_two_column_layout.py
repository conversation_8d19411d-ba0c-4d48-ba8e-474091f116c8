#!/usr/bin/env python3
"""
Demo script for the new two-column visualization layout
Shows different visualization modes with the improved interface
"""

import subprocess
import sys
import time

def run_demo(description, command, duration=15):
    """Run a demo with given description and command"""
    print(f"\n{'='*70}")
    print(f"DEMO: {description}")
    print(f"COMMAND: {command}")
    print(f"DURATION: {duration} seconds")
    print(f"{'='*70}")
    print("Starting demo... (Press Ctrl+C to skip to next demo)")
    
    try:
        # Run the command
        process = subprocess.Popen(
            command.split(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for specified duration
        try:
            stdout, stderr = process.communicate(timeout=duration)
            print("Demo completed successfully!")
            if stdout:
                print("Output:", stdout[-200:])  # Show last 200 chars
        except subprocess.TimeoutExpired:
            print(f"Demo ran for {duration} seconds, stopping...")
            process.terminate()
            try:
                stdout, stderr = process.communicate(timeout=3)
            except subprocess.TimeoutExpired:
                process.kill()
        
    except KeyboardInterrupt:
        print("\nDemo interrupted by user, moving to next...")
        if 'process' in locals():
            process.terminate()
    except Exception as e:
        print(f"Error running demo: {e}")

def main():
    """Main demo function"""
    print("Two-Column Visualization Layout Demo")
    print("====================================")
    print()
    print("This demo showcases the new two-column information panel layout:")
    print("- Left Column: SYSTEM & TASKS (System Stats + Task Pool)")
    print("- Right Column: AGV & PATHS (AGV Status + Path Planning)")
    print()
    print("Features:")
    print("✓ Wider layout (24x12) to accommodate two columns")
    print("✓ Better spacing to prevent information overlap")
    print("✓ Clear column headers with color coding")
    print("✓ Monospace font for better alignment")
    print("✓ All English text to avoid font issues")
    print()
    
    input("Press Enter to start the demos...")
    
    # Demo 1: Real-time mode with two-column layout
    run_demo(
        "Real-time Visualization with Two-Column Layout",
        "python run_simulation.py --config-template fast --case 1 --visualization real-time --max-time 45",
        duration=20
    )
    
    # Demo 2: Step mode for detailed observation
    run_demo(
        "Step-by-Step Mode (Good for Debugging)",
        "python run_simulation.py --config-template debug --case 1 --visualization step --max-time 30",
        duration=15
    )
    
    # Demo 3: Fast mode for quick testing
    run_demo(
        "Fast Mode (No Pauses, Quick Testing)",
        "python run_simulation.py --config-template fast --case 1 --visualization fast --max-time 60",
        duration=12
    )
    
    # Demo 4: Different case with custom settings
    run_demo(
        "Custom Configuration (Case 2, Different Speed)",
        "python run_simulation.py --case 2 --visualization real-time --speed 10 --max-time 40",
        duration=18
    )
    
    print(f"\n{'='*70}")
    print("Demo Complete!")
    print("Key improvements in the new layout:")
    print("1. Two-column design prevents information overlap")
    print("2. Larger figure size (24x12) provides more space")
    print("3. Clear column headers help organize information")
    print("4. Monospace font ensures proper alignment")
    print("5. English-only text avoids font display issues")
    print()
    print("The layout is now more readable and professional!")
    print(f"{'='*70}")

if __name__ == "__main__":
    main()
