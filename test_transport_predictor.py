#!/usr/bin/env python3
"""
Test script for Transport Time Predictor
运输时间预测器测试脚本
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple
import time

# 添加当前目录到路径
sys.path.append('.')

from transport_time_predictor import TransportTimePredictorInterface
from transport_predictor_config import get_config, get_experiment_config, validate_config


def test_basic_functionality():
    """测试基本功能"""
    print("Testing basic functionality...")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface()
        print("✓ Predictor created successfully")
        
        # 测试预测功能（使用未训练的模型）
        pred_time = predictor.predict_transport_time(
            start_point='P1',
            end_point='P5',
            agv_speed=1.0,
            start_time=0.0,
            obstacles=[]
        )
        print(f"✓ Basic prediction works: {pred_time:.2f}s")
        
        # 测试数据生成
        sample = predictor.data_generator.generate_sample()
        print(f"✓ Data generation works: sample keys = {list(sample.keys())}")
        
        return True
        
    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        return False


def test_model_training():
    """测试模型训练"""
    print("\nTesting model training...")
    
    try:
        # 使用调试配置进行快速训练
        config = get_config('debug')
        predictor = TransportTimePredictorInterface()
        predictor.config = config
        
        # 重新创建模型以使用新配置
        from transport_time_predictor import TransportTimePredictor
        predictor.model = TransportTimePredictor(config)
        predictor.model.to(predictor.device)
        
        print("Starting quick training...")
        start_time = time.time()
        
        # 快速训练
        predictor.train_model(num_train_samples=100, num_val_samples=50)
        
        training_time = time.time() - start_time
        print(f"✓ Training completed in {training_time:.2f}s")
        
        # 测试训练后的预测
        pred_time = predictor.predict_transport_time(
            start_point='P1',
            end_point='P10',
            agv_speed=1.0,
            start_time=0.0,
            obstacles=[]
        )
        print(f"✓ Post-training prediction: {pred_time:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"✗ Model training test failed: {e}")
        return False


def test_obstacle_impact():
    """测试障碍物对预测的影响"""
    print("\nTesting obstacle impact...")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface()
        
        # 无障碍物预测
        pred_no_obs = predictor.predict_transport_time(
            start_point='P1',
            end_point='P10',
            agv_speed=1.0,
            start_time=0.0,
            obstacles=[]
        )
        
        # 有障碍物预测
        obstacles = [('P5', 10.0, 25.0), ('P8', 15.0, 30.0)]
        pred_with_obs = predictor.predict_transport_time(
            start_point='P1',
            end_point='P10',
            agv_speed=1.0,
            start_time=0.0,
            obstacles=obstacles
        )
        
        print(f"No obstacles: {pred_no_obs:.2f}s")
        print(f"With obstacles: {pred_with_obs:.2f}s")
        print(f"Difference: {pred_with_obs - pred_no_obs:.2f}s")
        
        # 障碍物应该增加运输时间（在大多数情况下）
        if pred_with_obs >= pred_no_obs:
            print("✓ Obstacles correctly increase predicted time")
        else:
            print("⚠ Obstacles decreased predicted time (may be normal for untrained model)")
        
        return True
        
    except Exception as e:
        print(f"✗ Obstacle impact test failed: {e}")
        return False


def test_speed_impact():
    """测试AGV速度对预测的影响"""
    print("\nTesting AGV speed impact...")
    
    try:
        predictor = TransportTimePredictorInterface()
        
        speeds = [0.5, 1.0, 1.5, 2.0]
        predictions = []
        
        for speed in speeds:
            pred_time = predictor.predict_transport_time(
                start_point='P1',
                end_point='P10',
                agv_speed=speed,
                start_time=0.0,
                obstacles=[]
            )
            predictions.append(pred_time)
            print(f"Speed {speed} m/s: {pred_time:.2f}s")
        
        # 检查速度与时间的关系（一般来说速度越快时间越短）
        if all(predictions[i] >= predictions[i+1] for i in range(len(predictions)-1)):
            print("✓ Higher speed correctly reduces predicted time")
        else:
            print("⚠ Speed-time relationship not monotonic (may be normal for untrained model)")
        
        return True
        
    except Exception as e:
        print(f"✗ Speed impact test failed: {e}")
        return False


def test_data_generation():
    """测试数据生成功能"""
    print("\nTesting data generation...")
    
    try:
        predictor = TransportTimePredictorInterface()
        
        # 生成小批量数据
        dataset = predictor.data_generator.generate_dataset(100)
        print(f"✓ Generated {len(dataset)} samples")
        
        # 检查数据质量
        actual_times = [sample['actual_time'] for sample in dataset]
        print(f"Actual times - Min: {min(actual_times):.2f}s, Max: {max(actual_times):.2f}s, Mean: {np.mean(actual_times):.2f}s")
        
        # 检查数据结构
        sample = dataset[0]
        required_keys = ['graph_data', 'start_point', 'end_point', 'agv_speed', 
                        'start_time', 'distance', 'obstacle_features', 'actual_time']
        
        missing_keys = [key for key in required_keys if key not in sample]
        if not missing_keys:
            print("✓ All required keys present in samples")
        else:
            print(f"✗ Missing keys: {missing_keys}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Data generation test failed: {e}")
        return False


def test_model_save_load():
    """测试模型保存和加载"""
    print("\nTesting model save/load...")
    
    try:
        # 创建预测器
        predictor1 = TransportTimePredictorInterface()
        
        # 保存模型
        model_path = "test_model.pth"
        predictor1.save_model(model_path)
        print("✓ Model saved")
        
        # 加载模型
        predictor2 = TransportTimePredictorInterface(model_path=model_path)
        print("✓ Model loaded")
        
        # 比较预测结果
        pred1 = predictor1.predict_transport_time('P1', 'P5', 1.0, 0.0, [])
        pred2 = predictor2.predict_transport_time('P1', 'P5', 1.0, 0.0, [])
        
        if abs(pred1 - pred2) < 1e-6:
            print("✓ Loaded model produces identical predictions")
        else:
            print(f"⚠ Prediction difference: {abs(pred1 - pred2):.6f}")
        
        # 清理
        if os.path.exists(model_path):
            os.remove(model_path)
            print("✓ Test model file cleaned up")
        
        return True
        
    except Exception as e:
        print(f"✗ Model save/load test failed: {e}")
        return False


def test_configuration():
    """测试配置系统"""
    print("\nTesting configuration system...")
    
    try:
        # 测试不同配置
        configs = ['default', 'fast_training', 'high_accuracy', 'debug']
        
        for config_name in configs:
            config = get_config(config_name)
            validate_config(config)
            print(f"✓ Configuration '{config_name}' is valid")
        
        # 测试实验配置
        exp_config = get_experiment_config('quick_test')
        print(f"✓ Experiment configuration loaded: {exp_config['description']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


def run_performance_benchmark():
    """运行性能基准测试"""
    print("\nRunning performance benchmark...")
    
    try:
        predictor = TransportTimePredictorInterface()
        
        # 测试单次预测时间
        start_time = time.time()
        for _ in range(100):
            predictor.predict_transport_time('P1', 'P10', 1.0, 0.0, [])
        single_pred_time = (time.time() - start_time) / 100
        
        print(f"Average single prediction time: {single_pred_time*1000:.2f}ms")
        
        # 测试批量数据生成时间
        start_time = time.time()
        dataset = predictor.data_generator.generate_dataset(1000)
        data_gen_time = time.time() - start_time
        
        print(f"Time to generate 1000 samples: {data_gen_time:.2f}s")
        print(f"Average per sample: {data_gen_time/1000*1000:.2f}ms")
        
        return True
        
    except Exception as e:
        print(f"✗ Performance benchmark failed: {e}")
        return False


def main():
    """主测试函数"""
    print("Transport Time Predictor Test Suite")
    print("=" * 50)
    
    tests = [
        ("Basic Functionality", test_basic_functionality),
        ("Data Generation", test_data_generation),
        ("Configuration System", test_configuration),
        ("Obstacle Impact", test_obstacle_impact),
        ("Speed Impact", test_speed_impact),
        ("Model Save/Load", test_model_save_load),
        ("Performance Benchmark", run_performance_benchmark),
        ("Model Training", test_model_training),  # 最后测试，因为耗时较长
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ Test '{test_name}' crashed: {e}")
            results[test_name] = False
    
    # 总结结果
    print(f"\n{'='*50}")
    print("Test Results Summary:")
    print(f"{'='*50}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<25} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
