#!/usr/bin/env python3
"""
Test script for CASE configuration integration
测试CASE配置集成的脚本
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from transport_time_predictor import TransportTimePredictorInterface, load_case_config
from collections import defaultdict


def test_case_config_loading():
    """测试CASE配置加载"""
    print("=== Testing CASE Configuration Loading ===")
    
    try:
        # 加载CASE1配置
        case_config = load_case_config(1)
        
        print(f"✓ Successfully loaded CASE1 configuration")
        print(f"Task points: {len(case_config['task_points'])}")
        print(f"Channels: {len(case_config['channels'])}")
        print(f"Task types: {list(case_config['task_generation_params']['task_types'].keys())}")
        print(f"AGV types: {list(case_config['agv_params']['agv_types'].keys())}")
        
        # 验证任务点结构
        sample_point = list(case_config['task_points'].items())[0]
        print(f"Sample task point: {sample_point}")
        
        # 验证通道结构
        sample_channel = list(case_config['channels'].items())[0]
        print(f"Sample channel: {sample_channel}")
        
        return True
        
    except Exception as e:
        print(f"✗ CASE configuration loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_predictor_with_case_config():
    """测试使用CASE配置的预测器"""
    print("\n=== Testing Predictor with CASE Configuration ===")
    
    try:
        # 创建使用CASE1配置的预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        print(f"✓ Predictor created with CASE1 configuration")
        print(f"Graph structure loaded: {len(predictor.graph_structure['task_points'])} points")
        print(f"Data generator has CASE config: {predictor.data_generator.case_config is not None}")
        
        # 测试任务点选择
        task_generation_params = predictor.data_generator.task_generation_params
        if task_generation_params:
            print(f"Task generation parameters loaded: {list(task_generation_params['task_types'].keys())}")
        
        # 测试AGV参数
        agv_params = predictor.data_generator.agv_params
        if agv_params:
            print(f"AGV parameters loaded: {list(agv_params['agv_types'].keys())}")
        
        return True
        
    except Exception as e:
        print(f"✗ Predictor with CASE config failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_case_based_sample_generation():
    """测试基于CASE配置的样本生成"""
    print("\n=== Testing CASE-based Sample Generation ===")
    
    try:
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 生成多个样本并分析分布
        num_samples = 1000
        print(f"Generating {num_samples} samples...")
        
        start_points = []
        end_points = []
        agv_speeds = []
        
        for _ in range(num_samples):
            sample = predictor.data_generator.generate_sample()
            
            # 转换索引回点ID
            start_point_id = list(predictor.graph_structure['task_points'].keys())[sample['start_point']]
            end_point_id = list(predictor.graph_structure['task_points'].keys())[sample['end_point']]
            
            start_points.append(start_point_id)
            end_points.append(end_point_id)
            agv_speeds.append(sample['agv_speed'])
        
        # 分析起始点分布
        start_point_counts = defaultdict(int)
        for point in start_points:
            start_point_counts[point] += 1
        
        print(f"\nStart point distribution (top 10):")
        sorted_starts = sorted(start_point_counts.items(), key=lambda x: x[1], reverse=True)
        for point, count in sorted_starts[:10]:
            print(f"  {point}: {count} ({count/num_samples*100:.1f}%)")
        
        # 分析终点分布
        end_point_counts = defaultdict(int)
        for point in end_points:
            end_point_counts[point] += 1
        
        print(f"\nEnd point distribution (top 10):")
        sorted_ends = sorted(end_point_counts.items(), key=lambda x: x[1], reverse=True)
        for point, count in sorted_ends[:10]:
            print(f"  {point}: {count} ({count/num_samples*100:.1f}%)")
        
        # 分析AGV速度分布
        unique_speeds = list(set(agv_speeds))
        print(f"\nAGV speed distribution:")
        for speed in sorted(unique_speeds):
            count = agv_speeds.count(speed)
            print(f"  {speed} m/s: {count} ({count/num_samples*100:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"✗ CASE-based sample generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_task_type_distribution():
    """测试任务类型分布是否符合CASE配置"""
    print("\n=== Testing Task Type Distribution ===")
    
    try:
        predictor = TransportTimePredictorInterface(case_number=1)
        case_config = predictor.case_config
        
        # 获取CASE配置中的任务类型比例
        task_types = case_config['task_generation_params']['task_types']
        expected_ratios = {}
        for task_type, config in task_types.items():
            expected_ratios[task_type] = config['ratio']
        
        print(f"Expected task type ratios from CASE1:")
        for task_type, ratio in expected_ratios.items():
            print(f"  {task_type}: {ratio}")
        
        # 生成样本并统计实际分布
        num_samples = 2000
        task_type_counts = defaultdict(int)
        
        for _ in range(num_samples):
            # 模拟任务类型选择过程
            start_point, end_point = predictor.data_generator._select_task_points_from_case()
            
            # 根据起始点和终点推断任务类型
            task_type = infer_task_type(start_point, end_point, task_types)
            if task_type:
                task_type_counts[task_type] += 1
        
        print(f"\nActual task type distribution from {num_samples} samples:")
        total_classified = sum(task_type_counts.values())
        for task_type, count in task_type_counts.items():
            actual_ratio = count / total_classified
            expected_ratio = expected_ratios[task_type]
            print(f"  {task_type}: {count} ({actual_ratio:.3f}) - Expected: {expected_ratio:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Task type distribution test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def infer_task_type(start_point, end_point, task_types):
    """根据起始点和终点推断任务类型"""
    for task_type, config in task_types.items():
        start_candidates = config['start_point'].get('candidates', [])
        end_candidates = config['end_point'].get('candidates', [])
        
        start_distribution = config['start_point'].get('distribution', {})
        end_distribution = config['end_point'].get('distribution', {})
        
        # 检查是否匹配候选点
        start_match = start_point in start_candidates or start_point in start_distribution
        end_match = end_point in end_candidates or end_point in end_distribution
        
        if start_match and end_match:
            return task_type
    
    return None


def visualize_case_structure():
    """可视化CASE配置结构"""
    print("\n=== Visualizing CASE Structure ===")
    
    try:
        predictor = TransportTimePredictorInterface(case_number=1)
        case_config = predictor.case_config
        
        # 创建可视化
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 任务点分布
        task_points = case_config['task_points']
        x_coords = [point['x'] for point in task_points.values()]
        y_coords = [point['y'] for point in task_points.values()]
        
        axes[0, 0].scatter(x_coords, y_coords, c='blue', s=100, alpha=0.7)
        for point_id, coords in task_points.items():
            axes[0, 0].annotate(point_id, (coords['x'], coords['y']), 
                               xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        # 绘制通道
        channels = case_config['channels']
        for (start, end), _ in channels.items():
            start_coords = task_points[start]
            end_coords = task_points[end]
            axes[0, 0].plot([start_coords['x'], end_coords['x']], 
                           [start_coords['y'], end_coords['y']], 
                           'gray', alpha=0.5, linewidth=1)
        
        axes[0, 0].set_xlabel('X Coordinate')
        axes[0, 0].set_ylabel('Y Coordinate')
        axes[0, 0].set_title('CASE1 Task Points and Channels')
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].axis('equal')
        
        # 2. 任务类型比例
        task_types = case_config['task_generation_params']['task_types']
        type_names = list(task_types.keys())
        type_ratios = [task_types[name]['ratio'] for name in type_names]
        
        axes[0, 1].pie(type_ratios, labels=type_names, autopct='%1.1f%%')
        axes[0, 1].set_title('Task Type Distribution')
        
        # 3. AGV类型比例
        agv_types = case_config['agv_params']['agv_types']
        agv_names = list(agv_types.keys())
        agv_ratios = [agv_types[name]['ratio'] for name in agv_names]
        agv_speeds = [agv_types[name]['speed'] for name in agv_names]
        
        axes[1, 0].pie(agv_ratios, labels=[f"{name}\n({speed} m/s)" for name, speed in zip(agv_names, agv_speeds)], 
                       autopct='%1.1f%%')
        axes[1, 0].set_title('AGV Type Distribution')
        
        # 4. 障碍物类型比例
        obstacle_types = case_config['dynamic_obstacle_params']['obstacle_types']
        obs_names = list(obstacle_types.keys())
        obs_ratios = [obstacle_types[name]['ratio'] for name in obs_names]
        
        axes[1, 1].pie(obs_ratios, labels=obs_names, autopct='%1.1f%%')
        axes[1, 1].set_title('Obstacle Type Distribution')
        
        plt.tight_layout()
        plt.savefig('case1_structure_visualization.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print("✓ CASE structure visualization saved as 'case1_structure_visualization.png'")
        return True
        
    except Exception as e:
        print(f"✗ CASE structure visualization failed: {e}")
        return False


def main():
    """主测试函数"""
    print("CASE Configuration Integration Test")
    print("=" * 50)
    
    tests = [
        ("CASE Config Loading", test_case_config_loading),
        ("Predictor with CASE Config", test_predictor_with_case_config),
        ("CASE-based Sample Generation", test_case_based_sample_generation),
        ("Task Type Distribution", test_task_type_distribution),
        ("CASE Structure Visualization", visualize_case_structure),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ Test '{test_name}' crashed: {e}")
            results[test_name] = False
    
    # 总结结果
    print(f"\n{'='*50}")
    print("Test Results Summary:")
    print(f"{'='*50}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<30} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
