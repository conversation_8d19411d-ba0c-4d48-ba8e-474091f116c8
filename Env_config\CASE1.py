# 任务点配置
task_points = {
    # 第一行
    'P1': {'x': 0, 'y': 0},
    'P2': {'x': 2, 'y': 0},
    'P3': {'x': 4, 'y': 0},
    'P4': {'x': 6, 'y': 0},
    'P5': {'x': 8, 'y': 0},
    # 第二行
    'P6': {'x': 0, 'y': 2},
    'P7': {'x': 2, 'y': 2},
    'P8': {'x': 4, 'y': 2},
    'P9': {'x': 6, 'y': 2},
    'P10': {'x': 8, 'y': 2},
    # 第三行
    'P11': {'x': 0, 'y': 4},
    'P12': {'x': 2, 'y': 4},
    'P13': {'x': 4, 'y': 4},
    'P14': {'x': 6, 'y': 4},
    'P15': {'x': 8, 'y': 4},
    # 第四行
    'P16': {'x': 0, 'y': 6},
    'P17': {'x': 2, 'y': 6},
    'P18': {'x': 4, 'y': 6},
    'P19': {'x': 6, 'y': 6},
    'P20': {'x': 8, 'y': 6}
}

# 任务生成参数配置
task_generation_params = {
    # 全局参数
    'case_num': 1,  # 任务列表对应的实验case编号
    'total_tasks': 100,  # 总任务数量

    # 任务类型配置
    'task_types': {
        # 类型1: 快速运输任务
        'fast_transport': {
            # 任务比例，占总任务数量的百分比
            'ratio': 0.4,

            # 起始点选取配置（均匀分布）
            'start_point': {
                'mode': 'uniform',  # 均匀分布从点集中选取
                'candidates': ['P1', 'P2', 'P3', 'P4', 'P5']  # 候选起始点
            },

            # 终止点选取配置（均匀分布）
            'end_point': {
                'mode': 'uniform',  # 均匀分布从点集中选取
                'candidates': ['P16', 'P17', 'P18', 'P19', 'P20']  # 候选终止点
            },

            # 任务插入时间间隔（高斯分布）
            'time_interval': {
                'mean': 10.0,  # 平均时间间隔（秒）
                'std': 3.0  # 标准差（秒）
            },

            # 任务持续时间（高斯分布）
            'duration': {
                'mean': 60.0,  # 平均持续时间（秒）
                'std': 10.0  # 标准差（秒）
            }
        },

        # 类型2: 标准运输任务
        'standard_transport': {
            # 任务比例
            'ratio': 0.4,

            # 起始点选取配置（均匀分布）
            'start_point': {
                'mode': 'uniform',
                'candidates': ['P6', 'P7', 'P8', 'P9', 'P10']
            },

            # 终止点选取配置（均匀分布）
            'end_point': {
                'mode': 'uniform',
                'candidates': ['P11', 'P12', 'P13', 'P14', 'P15']
            },

            # 任务插入时间间隔（高斯分布）
            'time_interval': {
                'mean': 15.0,
                'std': 5.0
            },

            # 任务持续时间（高斯分布）
            'duration': {
                'mean': 90.0,
                'std': 15.0
            }
        },

        # 类型3: 特殊运输任务
        'special_transport': {
            # 任务比例
            'ratio': 0.2,

            # 起始点选取配置（指定概率分布）
            'start_point': {
                'mode': 'weighted',  # 按权重选取
                'distribution': {
                    'P1': 0.3,
                    'P5': 0.3,
                    'P16': 0.2,
                    'P20': 0.2
                }
            },

            # 终止点选取配置（指定概率分布）
            'end_point': {
                'mode': 'weighted',  # 按权重选取
                'distribution': {
                    'P8': 0.25,
                    'P13': 0.25,
                    'P12': 0.25,
                    'P17': 0.25
                }
            },

            # 任务插入时间间隔（高斯分布）
            'time_interval': {
                'mean': 30.0,
                'std': 10.0
            },

            # 任务持续时间（高斯分布）
            'duration': {
                'mean': 120.0,
                'std': 20.0
            }
        }
    }
}

# 动态障碍生成参数配置
dynamic_obstacle_params = {
    # 全局参数
    'total_obstacles': 20,  # 总障碍物数量

    # 障碍物类型配置
    'obstacle_types': {
        # 类型1: 小型障碍物
        'small_obstacle': {
            # 障碍物比例，占总障碍物数量的百分比
            'ratio': 0.4,

            # 障碍物中心坐标分布参数（均匀分布）
            'center': {
                'x': {'min': 1, 'max': 7},  # x坐标范围
                'y': {'min': 1, 'max': 5}   # y坐标范围
            },

            # 障碍物尺寸分布参数（高斯分布）
            'size': {
                'width': {'mean': 0.8, 'std': 0.1},   # 横向长度
                'height': {'mean': 0.8, 'std': 0.1}   # 纵向长度
            },

            # 插入时间间隔（高斯分布）
            'time_interval': {
                'mean': 15.0,  # 平均时间间隔（秒）
                'std': 5.0     # 标准差（秒）
            },

            # 持续时间（高斯分布）
            'duration': {
                'mean': 15.0,  # 平均持续时间（秒）
                'std': 3.0     # 标准差（秒）
            }
        },

        # 类型2: 中型障碍物
        'medium_obstacle': {
            # 障碍物比例
            'ratio': 0.4,

            # 障碍物中心坐标分布参数（均匀分布）
            'center': {
                'x': {'min': 0, 'max': 8},  # x坐标范围
                'y': {'min': 0, 'max': 6}   # y坐标范围
            },

            # 障碍物尺寸分布参数（高斯分布）
            'size': {
                'width': {'mean': 1.2, 'std': 0.2},   # 横向长度
                'height': {'mean': 1.2, 'std': 0.2}   # 纵向长度
            },

            # 插入时间间隔（高斯分布）
            'time_interval': {
                'mean': 25.0,  # 平均时间间隔（秒）
                'std': 8.0     # 标准差（秒）
            },

            # 持续时间（高斯分布）
            'duration': {
                'mean': 25.0,  # 平均持续时间（秒）
                'std': 5.0     # 标准差（秒）
            }
        },

        # 类型3: 大型障碍物
        'large_obstacle': {
            # 障碍物比例
            'ratio': 0.2,

            # 障碍物中心坐标分布参数（均匀分布）
            'center': {
                'x': {'min': 2, 'max': 6},  # x坐标范围
                'y': {'min': 2, 'max': 4}   # y坐标范围
            },

            # 障碍物尺寸分布参数（高斯分布）
            'size': {
                'width': {'mean': 1.8, 'std': 0.3},   # 横向长度
                'height': {'mean': 1.8, 'std': 0.3}   # 纵向长度
            },

            # 插入时间间隔（高斯分布）
            'time_interval': {
                'mean': 40.0,  # 平均时间间隔（秒）
                'std': 10.0    # 标准差（秒）
            },

            # 持续时间（高斯分布）
            'duration': {
                'mean': 40.0,  # 平均持续时间（秒）
                'std': 8.0     # 标准差（秒）
            }
        }
    }
}

# 通道配置
channels = {
    # 水平通道
    ('P1', 'P2'): {'distance': 2},
    ('P2', 'P3'): {'distance': 2},
    ('P3', 'P4'): {'distance': 2},
    ('P4', 'P5'): {'distance': 2},
    ('P6', 'P7'): {'distance': 2},
    ('P7', 'P8'): {'distance': 2},
    ('P8', 'P9'): {'distance': 2},
    ('P9', 'P10'): {'distance': 2},
    ('P11', 'P12'): {'distance': 2},
    ('P12', 'P13'): {'distance': 2},
    ('P13', 'P14'): {'distance': 2},
    ('P14', 'P15'): {'distance': 2},
    ('P16', 'P17'): {'distance': 2},
    ('P17', 'P18'): {'distance': 2},
    ('P18', 'P19'): {'distance': 2},
    ('P19', 'P20'): {'distance': 2},

    # 垂直通道
    ('P1', 'P6'): {'distance': 2},
    ('P6', 'P11'): {'distance': 2},
    ('P11', 'P16'): {'distance': 2},
    ('P2', 'P7'): {'distance': 2},
    ('P7', 'P12'): {'distance': 2},
    ('P12', 'P17'): {'distance': 2},
    ('P3', 'P8'): {'distance': 2},
    ('P8', 'P13'): {'distance': 2},
    ('P13', 'P18'): {'distance': 2},
    ('P4', 'P9'): {'distance': 2},
    ('P9', 'P14'): {'distance': 2},
    ('P14', 'P19'): {'distance': 2},
    ('P5', 'P10'): {'distance': 2},
    ('P10', 'P15'): {'distance': 2},
    ('P15', 'P20'): {'distance': 2}
}

# AGV参数配置
agv_params = {
    # 全局参数
    'total_agvs': 5,  # AGV总数量（默认5个）

    # AGV类型配置
    'agv_types': {
        # 类型1: 标准AGV
        'standard_agv': {
            # AGV比例，占总AGV数量的百分比
            'ratio': 0.6,

            # AGV速度（单位：米/秒）
            'speed': 1.0,

            # AGV初始点位配置
            'initial_positions': {
                'mode': 'fixed',  # 固定点位
                'positions': ['P1', 'P5', 'P16', 'P20']  # 候选初始点位
            }
        },

        # 类型2: 高速 AGV
        'fast_agv': {
            # AGV比例
            'ratio': 0.4,

            # AGV速度（单位：米/秒）
            'speed': 1.5,

            # AGV初始点位配置
            'initial_positions': {
                'mode': 'random',  # 随机选择点位
                'candidates': ['P6', 'P10', 'P11', 'P15']  # 候选初始点位
            }
        }
    }
}