#!/usr/bin/env python3
"""
Test script for enhanced obstacle features
测试增强障碍物特征的脚本
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from transport_time_predictor import TransportTimePredictorInterface
import torch


def test_node_obstacle_features():
    """测试节点障碍物特征"""
    print("=== Testing Node Obstacle Features ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 生成一个样本
        sample = predictor.data_generator.generate_sample()
        graph_data = sample['graph_data']
        
        print(f"Node features shape: {graph_data.x.shape}")
        print(f"Expected node features: 43 (3 basic + 10*4 obstacle info)")
        
        # 检查节点特征维度
        expected_node_features = 43  # [x, y, has_obstacle] + 10 * [start, end, relevance, coverage]
        actual_node_features = graph_data.x.shape[1]
        
        if actual_node_features == expected_node_features:
            print(f"✓ Node features dimension correct: {actual_node_features}")
        else:
            print(f"✗ Node features dimension mismatch: expected {expected_node_features}, got {actual_node_features}")
            return False
        
        # 分析节点特征
        print(f"\nNode features analysis:")
        print(f"Number of nodes: {graph_data.x.shape[0]}")
        
        # 检查基础特征
        for i in range(min(3, graph_data.x.shape[0])):
            node_features = graph_data.x[i]
            x, y, has_obstacle = node_features[:3]
            print(f"  Node {i}: position=({x:.2f}, {y:.2f}), has_obstacle={has_obstacle:.0f}")
            
            # 检查障碍物信息
            obstacle_count = 0
            for j in range(10):
                start_idx = 3 + j * 4
                obs_start, obs_end, relevance, coverage = node_features[start_idx:start_idx+4]
                if obs_start > 0 or obs_end > 0 or relevance > 0 or coverage > 0:
                    obstacle_count += 1
                    print(f"    Obstacle {j+1}: start={obs_start:.3f}, end={obs_end:.3f}, rel={relevance:.3f}, cov={coverage:.3f}")
            
            if obstacle_count == 0:
                print(f"    No obstacle information")
        
        return True
        
    except Exception as e:
        print(f"✗ Node obstacle features test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_edge_obstacle_features():
    """测试边障碍物特征"""
    print("\n=== Testing Edge Obstacle Features ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 生成一个样本
        sample = predictor.data_generator.generate_sample()
        graph_data = sample['graph_data']
        
        print(f"Edge features shape: {graph_data.edge_attr.shape}")
        print(f"Expected edge features: 42 (2 basic + 10*4 obstacle info)")
        
        # 检查边特征维度
        expected_edge_features = 42  # [distance, has_obstacle] + 10 * [start, end, relevance, coverage]
        actual_edge_features = graph_data.edge_attr.shape[1]
        
        if actual_edge_features == expected_edge_features:
            print(f"✓ Edge features dimension correct: {actual_edge_features}")
        else:
            print(f"✗ Edge features dimension mismatch: expected {expected_edge_features}, got {actual_edge_features}")
            return False
        
        # 分析边特征
        print(f"\nEdge features analysis:")
        print(f"Number of edges: {graph_data.edge_attr.shape[0]}")
        
        # 检查前几条边的特征
        for i in range(min(3, graph_data.edge_attr.shape[0])):
            edge_features = graph_data.edge_attr[i]
            distance, has_obstacle = edge_features[:2]
            print(f"  Edge {i}: distance={distance:.2f}, has_obstacle={has_obstacle:.0f}")
            
            # 检查障碍物信息
            obstacle_count = 0
            for j in range(10):
                start_idx = 2 + j * 4
                obs_start, obs_end, relevance, coverage = edge_features[start_idx:start_idx+4]
                if obs_start > 0 or obs_end > 0 or relevance > 0 or coverage > 0:
                    obstacle_count += 1
                    print(f"    Obstacle {j+1}: start={obs_start:.3f}, end={obs_end:.3f}, rel={relevance:.3f}, cov={coverage:.3f}")
            
            if obstacle_count == 0:
                print(f"    No obstacle information")
        
        return True
        
    except Exception as e:
        print(f"✗ Edge obstacle features test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_compatibility():
    """测试模型兼容性"""
    print("\n=== Testing Model Compatibility ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 生成一个样本
        sample = predictor.data_generator.generate_sample()
        
        print(f"Graph data created successfully")
        print(f"Node features: {sample['graph_data'].x.shape}")
        print(f"Edge features: {sample['graph_data'].edge_attr.shape}")
        print(f"Edge indices: {sample['graph_data'].edge_index.shape}")
        
        # 测试模型前向传播
        batch_data = [sample]
        
        try:
            predictions = predictor.model(batch_data)
            print(f"✓ Model forward pass successful")
            print(f"Predictions shape: {predictions.shape}")
            print(f"Sample prediction: {predictions[0].item():.4f}")
            return True
        except Exception as e:
            print(f"✗ Model forward pass failed: {e}")
            return False
        
    except Exception as e:
        print(f"✗ Model compatibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_obstacle_information_quality():
    """测试障碍物信息质量"""
    print("\n=== Testing Obstacle Information Quality ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 生成多个样本来分析障碍物信息
        num_samples = 100
        node_obstacle_counts = []
        edge_obstacle_counts = []
        
        for _ in range(num_samples):
            sample = predictor.data_generator.generate_sample()
            graph_data = sample['graph_data']
            
            # 统计节点障碍物信息
            node_count = 0
            for i in range(graph_data.x.shape[0]):
                node_features = graph_data.x[i]
                for j in range(10):
                    start_idx = 3 + j * 4
                    obs_start, obs_end, relevance, coverage = node_features[start_idx:start_idx+4]
                    if obs_start > 0 or obs_end > 0 or relevance > 0 or coverage > 0:
                        node_count += 1
            node_obstacle_counts.append(node_count)
            
            # 统计边障碍物信息
            edge_count = 0
            for i in range(graph_data.edge_attr.shape[0]):
                edge_features = graph_data.edge_attr[i]
                for j in range(10):
                    start_idx = 2 + j * 4
                    obs_start, obs_end, relevance, coverage = edge_features[start_idx:start_idx+4]
                    if obs_start > 0 or obs_end > 0 or relevance > 0 or coverage > 0:
                        edge_count += 1
            edge_obstacle_counts.append(edge_count)
        
        # 分析统计结果
        print(f"Obstacle information statistics over {num_samples} samples:")
        print(f"Node obstacles per sample: mean={np.mean(node_obstacle_counts):.2f}, std={np.std(node_obstacle_counts):.2f}")
        print(f"Edge obstacles per sample: mean={np.mean(edge_obstacle_counts):.2f}, std={np.std(edge_obstacle_counts):.2f}")
        print(f"Max node obstacles in a sample: {np.max(node_obstacle_counts)}")
        print(f"Max edge obstacles in a sample: {np.max(edge_obstacle_counts)}")
        
        # 检查是否有障碍物信息
        if np.mean(node_obstacle_counts) > 0 or np.mean(edge_obstacle_counts) > 0:
            print("✓ Obstacle information is being captured")
            return True
        else:
            print("⚠ No obstacle information found - this might be normal if obstacles don't overlap with nodes/edges")
            return True
        
    except Exception as e:
        print(f"✗ Obstacle information quality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_training_with_enhanced_features():
    """测试使用增强特征的训练"""
    print("\n=== Testing Training with Enhanced Features ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 使用小规模配置进行快速测试
        predictor.config.update({
            'num_epochs': 3,
            'batch_size': 8,
            'graph_hidden_dim': 32,
            'temporal_hidden_dim': 64
        })
        
        # 重新创建模型以使用新配置
        from transport_time_predictor import TransportTimePredictor
        predictor.model = TransportTimePredictor(predictor.config)
        predictor.model.to(predictor.device)
        
        print("Starting training with enhanced obstacle features...")
        
        # 训练模型
        predictor.train_model(
            num_train_samples=100,
            num_val_samples=25
        )
        
        print("✓ Training completed successfully with enhanced features")
        
        # 测试预测
        pred_time = predictor.predict_transport_time(
            start_point='P1',
            end_point='P10',
            agv_speed=1.0,
            start_time=0.0
        )
        
        print(f"Sample prediction: {pred_time:.4f}s")
        return True
        
    except Exception as e:
        print(f"✗ Training with enhanced features failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def visualize_obstacle_features():
    """可视化障碍物特征"""
    print("\n=== Visualizing Obstacle Features ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 生成样本
        sample = predictor.data_generator.generate_sample()
        graph_data = sample['graph_data']
        
        # 创建可视化
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 节点特征分布
        node_features = graph_data.x.numpy()
        x_coords = node_features[:, 0]
        y_coords = node_features[:, 1]
        has_obstacles = node_features[:, 2]
        
        scatter = axes[0, 0].scatter(x_coords, y_coords, c=has_obstacles, cmap='RdYlBu', s=100)
        axes[0, 0].set_xlabel('X Coordinate')
        axes[0, 0].set_ylabel('Y Coordinate')
        axes[0, 0].set_title('Nodes with Obstacle Information')
        plt.colorbar(scatter, ax=axes[0, 0], label='Has Obstacle')
        
        # 2. 边特征分布
        edge_features = graph_data.edge_attr.numpy()
        distances = edge_features[:, 0]
        has_edge_obstacles = edge_features[:, 1]
        
        axes[0, 1].hist(distances, bins=20, alpha=0.7, label='All Edges')
        obstacle_distances = distances[has_edge_obstacles > 0]
        if len(obstacle_distances) > 0:
            axes[0, 1].hist(obstacle_distances, bins=20, alpha=0.7, label='Edges with Obstacles')
        axes[0, 1].set_xlabel('Distance')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].set_title('Edge Distance Distribution')
        axes[0, 1].legend()
        
        # 3. 障碍物时间信息分布
        node_start_times = []
        node_end_times = []
        for i in range(node_features.shape[0]):
            for j in range(10):
                start_idx = 3 + j * 4
                obs_start, obs_end = node_features[i, start_idx:start_idx+2]
                if obs_start > 0 or obs_end > 0:
                    node_start_times.append(obs_start * 200)  # 反归一化
                    node_end_times.append(obs_end * 200)
        
        if len(node_start_times) > 0:
            axes[1, 0].hist(node_start_times, bins=15, alpha=0.7, label='Start Times')
            axes[1, 0].hist(node_end_times, bins=15, alpha=0.7, label='End Times')
            axes[1, 0].set_xlabel('Time (s)')
            axes[1, 0].set_ylabel('Frequency')
            axes[1, 0].set_title('Node Obstacle Time Distribution')
            axes[1, 0].legend()
        else:
            axes[1, 0].text(0.5, 0.5, 'No Node Obstacle\nTime Information', 
                           ha='center', va='center', transform=axes[1, 0].transAxes)
            axes[1, 0].set_title('Node Obstacle Time Distribution')
        
        # 4. 障碍物覆盖度和相关性分布
        coverages = []
        relevances = []
        for i in range(node_features.shape[0]):
            for j in range(10):
                start_idx = 3 + j * 4
                _, _, relevance, coverage = node_features[i, start_idx:start_idx+4]
                if relevance > 0 or coverage > 0:
                    relevances.append(relevance)
                    coverages.append(coverage)
        
        if len(coverages) > 0:
            axes[1, 1].scatter(relevances, coverages, alpha=0.6)
            axes[1, 1].set_xlabel('Relevance')
            axes[1, 1].set_ylabel('Coverage')
            axes[1, 1].set_title('Obstacle Relevance vs Coverage')
        else:
            axes[1, 1].text(0.5, 0.5, 'No Obstacle\nRelevance/Coverage\nInformation', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('Obstacle Relevance vs Coverage')
        
        plt.tight_layout()
        plt.savefig('obstacle_features_visualization.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print("✓ Obstacle features visualization saved as 'obstacle_features_visualization.png'")
        return True
        
    except Exception as e:
        print(f"✗ Obstacle features visualization failed: {e}")
        return False


def main():
    """主测试函数"""
    print("Enhanced Obstacle Features Test")
    print("=" * 50)
    
    tests = [
        ("Node Obstacle Features", test_node_obstacle_features),
        ("Edge Obstacle Features", test_edge_obstacle_features),
        ("Model Compatibility", test_model_compatibility),
        ("Obstacle Information Quality", test_obstacle_information_quality),
        ("Training with Enhanced Features", test_training_with_enhanced_features),
        ("Obstacle Features Visualization", visualize_obstacle_features),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ Test '{test_name}' crashed: {e}")
            results[test_name] = False
    
    # 总结结果
    print(f"\n{'='*50}")
    print("Test Results Summary:")
    print(f"{'='*50}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<35} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
