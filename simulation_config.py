"""
仿真配置文件
包含所有仿真相关的参数设置，方便调试和测试
"""

class SimulationConfig:
    """仿真配置类"""
    
    def __init__(self):
        # 基本仿真参数
        self.CASE_NUMBER = 1
        self.SIMULATION_TIME_STEP = 1  # 仿真时间步长（秒）
        self.MAX_SIMULATION_TIME = 700  # 最大仿真时间（秒）
        
        # 可视化参数
        self.VISUALIZATION_MODE = "real-time"  # real-time, step, fast, final, none
        self.VISUALIZATION_SPEED = 10.0  # 可视化速度倍数
        
        # 文件路径配置
        self.TASK_FILE_PATH = "output/tasks/tasks_case1_test.json"
        self.OBSTACLE_FILE_PATH = "output/obstacles/obstacles_case1_test.json"
        self.AGV_FILE_PATH = "output/tasks/tasks_case1_agvs_test.json"
        self.OUTPUT_DIR = "output"
        
        # AGV参数
        self.AGV_CONFIG = {
            'default_count': 5,
            'default_speed': 1.0,
            'speed_variance': 0.2,  # 速度变化范围
            'initial_positions': ['P1', 'P5', 'P16', 'P6', 'P6'],
            'agv_types': {
                'standard_agv': {'speed': 1.0, 'capacity': 1},
                'fast_agv': {'speed': 1.5, 'capacity': 1},
                'heavy_agv': {'speed': 0.8, 'capacity': 2}
            }
        }
        
        # 任务分配算法配置
        self.TASK_ALLOCATION_CONFIG = {
            'algorithm': 'greedy',  # greedy, hungarian, auction, custom
            'allocation_interval': 1.0,  # 任务分配检查间隔（秒）
            'priority_weights': {
                'distance': 0.6,
                'urgency': 0.3,
                'agv_efficiency': 0.1
            }
        }
        
        # 路径规划算法配置
        self.PATH_PLANNING_CONFIG = {
            'algorithm': 'astar',  # astar, dijkstra, rrt, custom
            'heuristic_weight': 1.0,
            'obstacle_buffer': 0.5,  # 障碍物缓冲区大小
            'path_smoothing': True,
            'dynamic_replanning': True
        }
        
        # 任务重调度算法配置
        self.TASK_RESCHEDULING_CONFIG = {
            'algorithm': 'hybrid',  # replan, reassign, hybrid, custom
            'replan_threshold': 2.0,  # 重规划阈值（秒）
            'max_replan_attempts': 3,
            'reassign_penalty': 10.0  # 重分配惩罚
        }
        
        # 性能指标配置
        self.PERFORMANCE_CONFIG = {
            'metrics_update_interval': 10.0,  # 性能指标更新间隔（秒）
            'save_detailed_logs': True,
            'log_level': 'INFO',  # DEBUG, INFO, WARNING, ERROR
            'export_format': 'json'  # json, csv, xlsx
        }
        
        # 调试和测试配置
        self.DEBUG_CONFIG = {
            'enable_debug_output': False,
            'debug_agv_paths': False,
            'debug_task_allocation': False,
            'debug_collision_detection': False,
            'save_intermediate_states': False,
            'state_save_interval': 60.0  # 状态保存间隔（秒）
        }
        
        # 实验配置
        self.EXPERIMENT_CONFIG = {
            'run_multiple_cases': False,
            'case_range': [1, 5],  # 运行的case范围
            'repeat_count': 1,  # 每个case重复次数
            'random_seed': 42,
            'parallel_execution': False
        }
        
        # 环境配置
        self.ENVIRONMENT_CONFIG = {
            'workspace_bounds': {'x_min': -2, 'x_max': 22, 'y_min': -2, 'y_max': 22},
            'grid_resolution': 0.1,
            'collision_detection_enabled': True,
            'deadlock_detection_enabled': True,
            'deadlock_timeout': 30.0  # 死锁检测超时（秒）
        }

    def update_case_number(self, case_number):
        """更新case编号并相应更新文件路径"""
        self.CASE_NUMBER = case_number
        self.TASK_FILE_PATH = f"output/tasks/tasks_case{case_number}_test.json"
        self.OBSTACLE_FILE_PATH = f"output/obstacles/obstacles_case{case_number}_test.json"
        self.AGV_FILE_PATH = f"output/tasks/tasks_case{case_number}_agvs_test.json"
    
    def set_fast_mode(self):
        """设置快速测试模式"""
        self.VISUALIZATION_MODE = "fast"
        self.VISUALIZATION_SPEED = 50.0
        self.MAX_SIMULATION_TIME = 600
        self.DEBUG_CONFIG['enable_debug_output'] = False
        self.PERFORMANCE_CONFIG['save_detailed_logs'] = False
    
    def set_debug_mode(self):
        """设置调试模式"""
        self.VISUALIZATION_MODE = "step"
        self.VISUALIZATION_SPEED = 1.0
        self.DEBUG_CONFIG['enable_debug_output'] = True
        self.DEBUG_CONFIG['debug_agv_paths'] = True
        self.DEBUG_CONFIG['debug_task_allocation'] = True
        self.DEBUG_CONFIG['save_intermediate_states'] = True
        self.PERFORMANCE_CONFIG['log_level'] = 'DEBUG'
    
    def set_production_mode(self):
        """设置生产模式"""
        self.VISUALIZATION_MODE = "none"
        self.DEBUG_CONFIG['enable_debug_output'] = False
        self.PERFORMANCE_CONFIG['save_detailed_logs'] = True
        self.PERFORMANCE_CONFIG['log_level'] = 'INFO'
    
    def get_config_dict(self):
        """获取配置字典"""
        return {
            'case_number': self.CASE_NUMBER,
            'simulation_time_step': self.SIMULATION_TIME_STEP,
            'max_simulation_time': self.MAX_SIMULATION_TIME,
            'visualization_mode': self.VISUALIZATION_MODE,
            'visualization_speed': self.VISUALIZATION_SPEED,
            'task_file_path': self.TASK_FILE_PATH,
            'obstacle_file_path': self.OBSTACLE_FILE_PATH,
            'agv_file_path': self.AGV_FILE_PATH,
            'output_dir': self.OUTPUT_DIR,
            'agv_config': self.AGV_CONFIG,
            'task_allocation_config': self.TASK_ALLOCATION_CONFIG,
            'path_planning_config': self.PATH_PLANNING_CONFIG,
            'task_rescheduling_config': self.TASK_RESCHEDULING_CONFIG,
            'performance_config': self.PERFORMANCE_CONFIG,
            'debug_config': self.DEBUG_CONFIG,
            'experiment_config': self.EXPERIMENT_CONFIG,
            'environment_config': self.ENVIRONMENT_CONFIG
        }
    
    def load_from_dict(self, config_dict):
        """从字典加载配置"""
        for key, value in config_dict.items():
            if hasattr(self, key.upper()):
                setattr(self, key.upper(), value)
    
    def save_to_file(self, filename):
        """保存配置到文件"""
        import json
        import os
        
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.get_config_dict(), f, indent=4, ensure_ascii=False)
    
    def load_from_file(self, filename):
        """从文件加载配置"""
        import json
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            self.load_from_dict(config_dict)
            return True
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return False

# 创建默认配置实例
default_config = SimulationConfig()

# 预定义的配置模板
FAST_TEST_CONFIG = SimulationConfig()
FAST_TEST_CONFIG.set_fast_mode()

DEBUG_CONFIG = SimulationConfig()
DEBUG_CONFIG.set_debug_mode()

PRODUCTION_CONFIG = SimulationConfig()
PRODUCTION_CONFIG.set_production_mode()

# 配置模板字典
CONFIG_TEMPLATES = {
    'default': default_config,
    'fast': FAST_TEST_CONFIG,
    'debug': DEBUG_CONFIG,
    'production': PRODUCTION_CONFIG
}

def get_config(template_name='default'):
    """获取指定模板的配置"""
    if template_name in CONFIG_TEMPLATES:
        return CONFIG_TEMPLATES[template_name]
    else:
        print(f"未知的配置模板: {template_name}，使用默认配置")
        return default_config

def create_custom_config(**kwargs):
    """创建自定义配置"""
    config = SimulationConfig()
    for key, value in kwargs.items():
        if hasattr(config, key.upper()):
            setattr(config, key.upper(), value)
    return config
