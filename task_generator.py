from task import Task
import random
# 如果没有numpy，使用自定义的高斯分布函数
import math

def normal_random(mean, std):
    """生成满足高斯分布的随机数

    Args:
        mean: 平均值
        std: 标准差

    Returns:
        float: 随机数
    """
    # Box-Muller变换
    u1 = random.random()
    u2 = random.random()
    z0 = math.sqrt(-2.0 * math.log(u1)) * math.cos(2.0 * math.pi * u2)
    return mean + z0 * std

class TaskGenerator:
    def __init__(self, task_generation_params):
        # 任务生成参数配置表
        self.params = task_generation_params
        # 任务列表
        self.task_list = []
        # 任务case编号
        self.case_num = self.params.get('case_num', 1)
        # 总任务数量
        self.total_tasks = self.params.get('total_tasks', 100)
        # 任务类型配置
        self.task_types = self.params.get('task_types', {})
        # 当前时间（模拟相对时间，从0开始）
        self.current_time = 0.0
        # 随机数生成器
        self.random = random.Random()

    def generate_tasks(self):
        """根据配置生成任务列表"""
        # 清空任务列表
        self.task_list = []

        # 计算每种任务类型的数量
        task_counts = {}
        remaining_tasks = self.total_tasks

        # 先分配有比例的任务类型
        for task_type, config in self.task_types.items():
            ratio = config.get('ratio', 0)
            count = int(self.total_tasks * ratio)
            task_counts[task_type] = count
            remaining_tasks -= count

        # 如果还有剩余任务，分配给第一个任务类型
        if remaining_tasks > 0 and self.task_types:
            first_type = list(self.task_types.keys())[0]
            task_counts[first_type] += remaining_tasks

        # 生成每种类型的任务
        all_tasks = []
        for task_type, count in task_counts.items():
            if task_type in self.task_types and count > 0:
                type_tasks = self._generate_tasks_by_type(task_type, count)
                all_tasks.extend(type_tasks)

        # 按插入时间排序
        all_tasks.sort(key=lambda x: x['insert_time'])

        # 创建任务对象
        task_id = 0
        for task_data in all_tasks:
            task = Task(
                task_id=f"task_{task_id}",
                start_point=task_data['start_point'],
                end_point=task_data['end_point'],
                insert_time=task_data['insert_time'],
                ddl=task_data['ddl'],
                task_type=task_data['task_type']
            )
            self.task_list.append(task)
            task_id += 1

        return self.task_list

    def _generate_tasks_by_type(self, task_type, count):
        """根据任务类型生成指定数量的任务

        Args:
            task_type: 任务类型
            count: 任务数量

        Returns:
            list: 任务数据列表
        """
        if task_type not in self.task_types:
            return []

        config = self.task_types[task_type]
        tasks = []

        # 当前时间点
        current_time = self.current_time

        for i in range(count):
            # 生成起始点
            start_point = self._select_point(config.get('start_point', {}))

            # 生成终止点
            end_point = self._select_point(config.get('end_point', {}))

            # 生成插入时间间隔
            time_interval_config = config.get('time_interval', {'mean': 10.0, 'std': 3.0})
            time_interval = max(1, normal_random(
                time_interval_config.get('mean', 10.0),
                time_interval_config.get('std', 3.0)
            ))

            # 计算插入时间（相对时间，秒）
            insert_time = current_time + time_interval
            current_time = insert_time

            # 生成任务持续时间
            duration_config = config.get('duration', {'mean': 60.0, 'std': 10.0})
            duration = max(1, normal_random(
                duration_config.get('mean', 60.0),
                duration_config.get('std', 10.0)
            ))

            # 计算DDL（相对时间，秒）
            ddl = insert_time + duration

            # 创建任务数据
            task_data = {
                'task_type': task_type,
                'start_point': start_point,
                'end_point': end_point,
                'insert_time': insert_time,
                'ddl': ddl,
                'duration': duration
            }

            tasks.append(task_data)

        return tasks

    def _select_point(self, point_config):
        """根据配置选择点

        Args:
            point_config: 点选取配置

        Returns:
            str: 选取的点ID
        """
        mode = point_config.get('mode', 'uniform')

        if mode == 'uniform':
            # 均匀分布选取
            candidates = point_config.get('candidates', [])
            if candidates:
                return self.random.choice(candidates)

        elif mode == 'weighted':
            # 按权重选取
            distribution = point_config.get('distribution', {})
            if distribution:
                items = list(distribution.items())
                points = [item[0] for item in items]
                weights = [item[1] for item in items]
                return self.random.choices(points, weights=weights, k=1)[0]

        # 默认返回第一个点
        return 'P1'

    def get_task_list(self):
        """获取任务列表"""
        return [task.get_task_info() for task in self.task_list]

    def save_task_list(self, file_path):
        """将任务列表存储到文件

        Args:
            file_path: 存储文件路径
        """
        # 获取任务数据
        task_data = [task.get_task_info() for task in self.task_list]

        # 确保时间字段是浮点数，表示相对时间（秒）
        for task in task_data:
            # 确保插入时间和DDL是浮点数
            if not isinstance(task['insert_time'], (int, float)):
                task['insert_time'] = 0.0
            if not isinstance(task['ddl'], (int, float)):
                task['ddl'] = task['duration'] if isinstance(task['duration'], (int, float)) else 60.0

        # 使用全局配置中的保存函数
        import config
        config.save_data_to_file(task_data, file_path)