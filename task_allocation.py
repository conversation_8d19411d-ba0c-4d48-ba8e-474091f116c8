"""
任务分配算法接口
负责将任务分配给空闲的AGV
"""

class TaskAllocator:
    def __init__(self, algorithm="greedy"):
        """
        初始化任务分配器
        
        Args:
            algorithm: 任务分配算法，可选值：
                - "greedy": 贪心算法，选择最近的AGV分配任务
                - "hungarian": 匈牙利算法，全局最优分配
                - "auction": 拍卖算法
                - "custom": 自定义算法
        """
        self.algorithm = algorithm
    
    def allocate(self, idle_agvs, task_pool, obstacle_pool, path_planner, current_time):
        """
        为空闲的AGV分配任务
        
        Args:
            idle_agvs: 空闲AGV列表
            task_pool: 任务池
            obstacle_pool: 障碍物池
            path_planner: 路径规划器
            current_time: 当前时间
            
        Returns:
            dict: AGV ID到任务ID的映射，表示分配结果
        """
        if not idle_agvs or not task_pool:
            return {}
        
        if self.algorithm == "greedy":
            return self._greedy_allocation(idle_agvs, task_pool, obstacle_pool, path_planner, current_time)
        elif self.algorithm == "hungarian":
            return self._hungarian_allocation(idle_agvs, task_pool, obstacle_pool, path_planner, current_time)
        elif self.algorithm == "auction":
            return self._auction_allocation(idle_agvs, task_pool, obstacle_pool, path_planner, current_time)
        elif self.algorithm == "custom":
            return self._custom_allocation(idle_agvs, task_pool, obstacle_pool, path_planner, current_time)
        else:
            # 默认使用贪心算法
            return self._greedy_allocation(idle_agvs, task_pool, obstacle_pool, path_planner, current_time)
    
    def _greedy_allocation(self, idle_agvs, task_pool, obstacle_pool, path_planner, current_time):
        """
        贪心算法分配任务
        选择最近的AGV分配给最早插入的任务
        
        Args:
            idle_agvs: 空闲AGV列表
            task_pool: 任务池
            obstacle_pool: 障碍物池
            path_planner: 路径规划器
            current_time: 当前时间
            
        Returns:
            dict: AGV ID到任务ID的映射，表示分配结果
        """
        allocations = {}
        
        # 按插入时间排序任务
        sorted_tasks = sorted(task_pool, key=lambda t: t.insert_time)
        
        for task in sorted_tasks:
            if not idle_agvs:
                break
            
            # 计算每个AGV到任务起点的距离
            agv_distances = []
            for agv in idle_agvs:
                # 计算AGV到任务起点的路径
                path = path_planner.plan_path(
                    start=agv.position,
                    end=task.start_point,
                    obstacle_pool=obstacle_pool,
                    current_time=current_time
                )
                
                # 如果无法规划路径，距离设为无穷大
                if not path:
                    distance = float('inf')
                else:
                    distance = path_planner.calculate_path_length(path)
                
                agv_distances.append((agv, distance))
            
            # 选择距离最短的AGV
            agv_distances.sort(key=lambda x: x[1])
            
            if agv_distances and agv_distances[0][1] < float('inf'):
                best_agv = agv_distances[0][0]
                
                # 分配任务
                allocations[best_agv.id] = task.id
                
                # 从空闲AGV列表中移除已分配的AGV
                idle_agvs.remove(best_agv)
        
        return allocations
    
    def _hungarian_allocation(self, idle_agvs, task_pool, obstacle_pool, path_planner, current_time):
        """
        匈牙利算法分配任务（全局最优分配）
        
        Args:
            idle_agvs: 空闲AGV列表
            task_pool: 任务池
            obstacle_pool: 障碍物池
            path_planner: 路径规划器
            current_time: 当前时间
            
        Returns:
            dict: AGV ID到任务ID的映射，表示分配结果
        """
        # TODO: 实现匈牙利算法
        # 这里简单地调用贪心算法作为占位符
        return self._greedy_allocation(idle_agvs, task_pool, obstacle_pool, path_planner, current_time)
    
    def _auction_allocation(self, idle_agvs, task_pool, obstacle_pool, path_planner, current_time):
        """
        拍卖算法分配任务
        
        Args:
            idle_agvs: 空闲AGV列表
            task_pool: 任务池
            obstacle_pool: 障碍物池
            path_planner: 路径规划器
            current_time: 当前时间
            
        Returns:
            dict: AGV ID到任务ID的映射，表示分配结果
        """
        # TODO: 实现拍卖算法
        # 这里简单地调用贪心算法作为占位符
        return self._greedy_allocation(idle_agvs, task_pool, obstacle_pool, path_planner, current_time)
    
    def _custom_allocation(self, idle_agvs, task_pool, obstacle_pool, path_planner, current_time):
        """
        自定义算法分配任务
        
        Args:
            idle_agvs: 空闲AGV列表
            task_pool: 任务池
            obstacle_pool: 障碍物池
            path_planner: 路径规划器
            current_time: 当前时间
            
        Returns:
            dict: AGV ID到任务ID的映射，表示分配结果
        """
        # TODO: 实现自定义算法
        # 这里简单地调用贪心算法作为占位符
        return self._greedy_allocation(idle_agvs, task_pool, obstacle_pool, path_planner, current_time)
