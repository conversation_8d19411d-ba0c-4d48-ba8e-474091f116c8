"""
测试全局配置文件的使用
"""
import config
from task_generator import TaskGenerator
from DO_generator import DOGenerator
from agv_generator import AGVGenerator

def main():
    # 设置实验case编号
    config.set_case_number(1)
    print(f"当前实验case编号: {config.EXPERIMENT['case_number']}")

    # 加载环境配置
    env_config = config.load_env_config()
    print(f"加载环境配置文件: {config.get_env_config_path()}")

    # 打印任务点配置
    print(f"任务点数量: {len(env_config.task_points)}")

    # 打印通道配置
    print(f"通道数量: {len(env_config.channels)}")

    # 打印任务生成参数
    print(f"任务生成参数: {env_config.task_generation_params}")

    # 打印动态障碍生成参数
    print(f"动态障碍生成参数: {env_config.dynamic_obstacle_params}")

    # 生成任务
    task_generator = TaskGenerator(env_config.task_generation_params)
    task_generator.generate_tasks()

    # 保存任务到配置的路径
    task_file_path = config.get_task_output_path("test")
    task_generator.save_task_list(task_file_path)
    print(f"任务已保存到: {task_file_path}")

    # 生成动态障碍物
    do_generator = DOGenerator(env_config.dynamic_obstacle_params)
    do_generator.generate_obstacles()

    # 保存障碍物到配置的路径
    obstacle_file_path = config.get_obstacle_output_path("test")
    do_generator.save_obstacle_list(obstacle_file_path)
    print(f"障碍物已保存到: {obstacle_file_path}")

    # 打印AGV参数
    print(f"AGV参数: {env_config.agv_params}")

    # 生成AGV
    agv_generator = AGVGenerator(env_config.agv_params, env_config.task_points)
    agv_generator.generate_agvs()

    # 保存AGV到配置的路径
    agv_file_path = config.get_task_output_path("agvs_test")
    agv_generator.save_agv_list(agv_file_path)
    print(f"AGV已保存到: {agv_file_path}")

if __name__ == "__main__":
    main()