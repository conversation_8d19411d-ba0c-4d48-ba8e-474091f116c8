class Task:
    def __init__(self, task_id, start_point, end_point, insert_time, ddl, task_type=None):
        # 任务ID
        self.id = task_id
        # 任务类型
        self.task_type = task_type
        # 起始点
        self.start_point = start_point
        # 目标点
        self.end_point = end_point
        # 插入时间
        self.insert_time = insert_time
        # DDL（截止时间）
        self.ddl = ddl
        # 任务持续时间（秒）
        if isinstance(ddl, (int, float)) and isinstance(insert_time, (int, float)):
            # 如果ddl和insert_time都是数字（相对时间）
            self.duration = ddl - insert_time
        else:
            # 兼容旧版本的时间对象
            try:
                if hasattr(ddl, 'total_seconds') and hasattr(insert_time, 'total_seconds'):
                    self.duration = (ddl - insert_time).total_seconds()
                else:
                    self.duration = 60.0  # 默认值
            except:
                self.duration = 60.0  # 默认值
        # 任务状态：'waiting'（等待分配）, 'assigned'（已分配）, 'in_progress'（执行中）, 'completed'（已完成）
        self.status = 'waiting'
        # 分配的AGV ID
        self.assigned_agv = None
        # 任务是否已分配
        self.is_assigned = False
        # 任务是否已完成
        self.is_completed = False

    def assign_to_agv(self, agv_id):
        """将任务分配给指定的AGV

        Args:
            agv_id: AGV ID
        """
        self.assigned_agv = agv_id
        self.is_assigned = True
        self.status = 'assigned'

    def start_execution(self):
        """开始执行任务"""
        self.status = 'in_progress'

    def complete_task(self):
        """完成任务"""
        self.status = 'completed'
        self.is_completed = True

    def get_task_info(self):
        """获取任务信息

        Returns:
            dict: 任务的当前信息
        """
        return {
            'id': self.id,
            'task_type': self.task_type,
            'start_point': self.start_point,
            'end_point': self.end_point,
            'insert_time': self.insert_time,
            'ddl': self.ddl,
            'duration': self.duration,
            'status': self.status,
            'assigned_agv': self.assigned_agv,
            'is_assigned': self.is_assigned,
            'is_completed': self.is_completed
        }