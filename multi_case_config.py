"""
Multi-CASE Training Configuration
多CASE训练配置文件
"""

# Multi-CASE Training Configurations
MULTI_CASE_CONFIGS = {
    'single_case': {
        'description': 'Single CASE training (baseline)',
        'train_cases': [1],
        'val_cases': [1],
        'test_cases': [1],
        'samples_per_case': 10000,
        'val_ratio': 0.2,
        'test_ratio': 0.1
    },
    
    'multi_case_balanced': {
        'description': 'Balanced multi-CASE training',
        'train_cases': [1, 2, 3],  # 可根据实际可用CASE调整
        'val_cases': [1, 2, 3],
        'test_cases': [1, 2, 3],
        'samples_per_case': 5000,
        'val_ratio': 0.2,
        'test_ratio': 0.1
    },
    
    'cross_case_validation': {
        'description': 'Cross-CASE validation (train on some, test on others)',
        'train_cases': [1, 2],
        'val_cases': [1, 2],
        'test_cases': [3],
        'samples_per_case': 8000,
        'val_ratio': 0.2,
        'test_ratio': 0.15
    },
    
    'incremental_training': {
        'description': 'Incremental training (start with one CASE, add more)',
        'train_cases': [1],  # 可以动态扩展
        'val_cases': [1],
        'test_cases': [1, 2, 3],
        'samples_per_case': 6000,
        'val_ratio': 0.2,
        'test_ratio': 0.1
    },
    
    'domain_adaptation': {
        'description': 'Domain adaptation (heavy on source, light on target)',
        'train_cases': [1],  # 源域
        'val_cases': [1, 2],
        'test_cases': [2, 3],  # 目标域
        'samples_per_case': 10000,
        'val_ratio': 0.15,
        'test_ratio': 0.2
    }
}

# Training Strategies
TRAINING_STRATEGIES = {
    'uniform_sampling': {
        'description': 'Uniform sampling from all CASEs',
        'sampling_method': 'uniform',
        'case_weights': None  # Equal weights
    },
    
    'weighted_sampling': {
        'description': 'Weighted sampling based on CASE complexity',
        'sampling_method': 'weighted',
        'case_weights': {1: 1.0, 2: 1.5, 3: 2.0}  # 可根据CASE复杂度调整
    },
    
    'sequential_sampling': {
        'description': 'Sequential sampling (one CASE at a time)',
        'sampling_method': 'sequential',
        'case_weights': None
    },
    
    'adaptive_sampling': {
        'description': 'Adaptive sampling based on performance',
        'sampling_method': 'adaptive',
        'case_weights': None  # 动态调整
    }
}

# Simulation Configurations
SIMULATION_CONFIGS = {
    'single_case_sim': {
        'description': 'Single CASE simulation',
        'case_sequence': [1] * 100,  # 100步都使用CASE1
        'switch_frequency': 'never'
    },
    
    'periodic_switching': {
        'description': 'Periodic CASE switching',
        'case_sequence': [1, 1, 2, 2, 3, 3] * 20,  # 周期性切换
        'switch_frequency': 'every_2_steps'
    },
    
    'random_switching': {
        'description': 'Random CASE switching',
        'case_sequence': 'random',  # 随机生成
        'switch_frequency': 'random',
        'case_pool': [1, 2, 3],
        'switch_probability': 0.1  # 每步10%概率切换
    },
    
    'scenario_based': {
        'description': 'Scenario-based CASE switching',
        'case_sequence': 'scenario',  # 基于场景
        'scenarios': {
            'normal_operation': {'case': 1, 'duration': 50},
            'high_traffic': {'case': 2, 'duration': 30},
            'maintenance': {'case': 3, 'duration': 20}
        }
    }
}

# Model Adaptation Strategies
ADAPTATION_STRATEGIES = {
    'no_adaptation': {
        'description': 'No adaptation (use pre-trained model)',
        'adaptation_method': 'none',
        'adaptation_samples': 0
    },
    
    'fine_tuning': {
        'description': 'Fine-tuning on target CASE',
        'adaptation_method': 'fine_tune',
        'adaptation_samples': 1000,
        'learning_rate_factor': 0.1,
        'freeze_layers': ['graph_encoder']  # 可选择冻结某些层
    },
    
    'online_learning': {
        'description': 'Online learning during simulation',
        'adaptation_method': 'online',
        'adaptation_samples': 100,
        'update_frequency': 10,  # 每10步更新一次
        'learning_rate_factor': 0.01
    },
    
    'meta_learning': {
        'description': 'Meta-learning for fast adaptation',
        'adaptation_method': 'meta',
        'adaptation_samples': 500,
        'meta_learning_rate': 0.001,
        'inner_learning_rate': 0.01
    }
}

# Evaluation Metrics
EVALUATION_METRICS = {
    'basic_metrics': [
        'mae',  # Mean Absolute Error
        'mse',  # Mean Squared Error
        'rmse', # Root Mean Squared Error
        'mape'  # Mean Absolute Percentage Error
    ],
    
    'cross_case_metrics': [
        'cross_case_mae',      # MAE across different CASEs
        'case_transfer_score', # Transfer learning effectiveness
        'adaptation_speed',    # How quickly model adapts to new CASE
        'stability_score'      # Prediction stability across CASEs
    ],
    
    'simulation_metrics': [
        'real_time_performance',  # Real-time prediction speed
        'memory_usage',          # Memory consumption
        'adaptation_overhead',   # Time cost of CASE switching
        'prediction_consistency' # Consistency before/after switching
    ]
}

# Experiment Configurations
EXPERIMENT_CONFIGS = {
    'baseline_experiment': {
        'description': 'Baseline single-CASE experiment',
        'multi_case_config': 'single_case',
        'training_strategy': 'uniform_sampling',
        'simulation_config': 'single_case_sim',
        'adaptation_strategy': 'no_adaptation',
        'metrics': ['basic_metrics']
    },
    
    'multi_case_experiment': {
        'description': 'Multi-CASE training experiment',
        'multi_case_config': 'multi_case_balanced',
        'training_strategy': 'uniform_sampling',
        'simulation_config': 'periodic_switching',
        'adaptation_strategy': 'no_adaptation',
        'metrics': ['basic_metrics', 'cross_case_metrics']
    },
    
    'transfer_learning_experiment': {
        'description': 'Transfer learning experiment',
        'multi_case_config': 'cross_case_validation',
        'training_strategy': 'weighted_sampling',
        'simulation_config': 'scenario_based',
        'adaptation_strategy': 'fine_tuning',
        'metrics': ['basic_metrics', 'cross_case_metrics']
    },
    
    'online_adaptation_experiment': {
        'description': 'Online adaptation experiment',
        'multi_case_config': 'incremental_training',
        'training_strategy': 'adaptive_sampling',
        'simulation_config': 'random_switching',
        'adaptation_strategy': 'online_learning',
        'metrics': ['basic_metrics', 'cross_case_metrics', 'simulation_metrics']
    }
}


def get_multi_case_config(config_name: str) -> dict:
    """获取多CASE配置"""
    if config_name not in MULTI_CASE_CONFIGS:
        available = list(MULTI_CASE_CONFIGS.keys())
        raise ValueError(f"Unknown multi-CASE config: {config_name}. Available: {available}")
    
    return MULTI_CASE_CONFIGS[config_name]


def get_training_strategy(strategy_name: str) -> dict:
    """获取训练策略"""
    if strategy_name not in TRAINING_STRATEGIES:
        available = list(TRAINING_STRATEGIES.keys())
        raise ValueError(f"Unknown training strategy: {strategy_name}. Available: {available}")
    
    return TRAINING_STRATEGIES[strategy_name]


def get_simulation_config(config_name: str) -> dict:
    """获取仿真配置"""
    if config_name not in SIMULATION_CONFIGS:
        available = list(SIMULATION_CONFIGS.keys())
        raise ValueError(f"Unknown simulation config: {config_name}. Available: {available}")
    
    return SIMULATION_CONFIGS[config_name]


def get_experiment_config(experiment_name: str) -> dict:
    """获取完整实验配置"""
    if experiment_name not in EXPERIMENT_CONFIGS:
        available = list(EXPERIMENT_CONFIGS.keys())
        raise ValueError(f"Unknown experiment: {experiment_name}. Available: {available}")
    
    config = EXPERIMENT_CONFIGS[experiment_name].copy()
    
    # 展开配置引用
    config['multi_case_details'] = get_multi_case_config(config['multi_case_config'])
    config['training_details'] = get_training_strategy(config['training_strategy'])
    config['simulation_details'] = get_simulation_config(config['simulation_config'])
    config['adaptation_details'] = ADAPTATION_STRATEGIES[config['adaptation_strategy']]
    config['metric_details'] = {
        metric_group: EVALUATION_METRICS[metric_group] 
        for metric_group in config['metrics']
    }
    
    return config


def list_available_configs():
    """列出所有可用配置"""
    print("Available Multi-CASE Configurations:")
    print("=" * 50)
    
    print("\n1. Multi-CASE Training Configs:")
    for name, config in MULTI_CASE_CONFIGS.items():
        print(f"  {name}: {config['description']}")
    
    print("\n2. Training Strategies:")
    for name, strategy in TRAINING_STRATEGIES.items():
        print(f"  {name}: {strategy['description']}")
    
    print("\n3. Simulation Configs:")
    for name, config in SIMULATION_CONFIGS.items():
        print(f"  {name}: {config['description']}")
    
    print("\n4. Adaptation Strategies:")
    for name, strategy in ADAPTATION_STRATEGIES.items():
        print(f"  {name}: {strategy['description']}")
    
    print("\n5. Experiment Configs:")
    for name, experiment in EXPERIMENT_CONFIGS.items():
        print(f"  {name}: {experiment['description']}")


def validate_case_availability(case_numbers: list) -> list:
    """验证CASE可用性"""
    import os
    
    available_cases = []
    for case_num in case_numbers:
        case_file = f"ENV_CONFIG/CASE{case_num}.py"
        obstacle_file = f"output/obstacles/obstacles_case{case_num}.json"
        
        if os.path.exists(case_file):
            available_cases.append(case_num)
            if not os.path.exists(obstacle_file):
                print(f"Warning: CASE{case_num} config exists but obstacle file missing")
        else:
            print(f"Warning: CASE{case_num} config file not found")
    
    return available_cases


if __name__ == "__main__":
    # 演示配置使用
    print("Multi-CASE Training Configuration Demo")
    print("=" * 50)
    
    # 列出所有配置
    list_available_configs()
    
    # 演示实验配置
    print(f"\n{'='*50}")
    print("Example Experiment Configuration:")
    print(f"{'='*50}")
    
    experiment = get_experiment_config('multi_case_experiment')
    print(f"Experiment: {experiment['description']}")
    print(f"Train CASEs: {experiment['multi_case_details']['train_cases']}")
    print(f"Samples per CASE: {experiment['multi_case_details']['samples_per_case']}")
    print(f"Training strategy: {experiment['training_details']['description']}")
    print(f"Simulation: {experiment['simulation_details']['description']}")
