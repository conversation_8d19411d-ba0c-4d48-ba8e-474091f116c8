#!/usr/bin/env python3
"""
Test script for obstacle loading from files
测试从文件加载障碍物的功能
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from transport_time_predictor import TransportTimePredictorInterface, create_sample_graph_structure


def test_obstacle_loading():
    """测试障碍物加载功能"""
    print("=== 测试障碍物加载功能 ===")
    
    try:
        # 创建预测器（使用case1的障碍物数据）
        predictor = TransportTimePredictorInterface(case_number=1)
        
        print(f"成功创建预测器，case_number = {predictor.case_number}")
        print(f"加载的障碍物数量: {len(predictor.data_generator.obstacles_data)}")
        
        # 显示前几个障碍物的信息
        print("\n前5个障碍物信息:")
        for i, obs_data in enumerate(predictor.data_generator.obstacles_data[:5]):
            print(f"  障碍物 {i+1}:")
            print(f"    ID: {obs_data['id']}")
            print(f"    类型: {obs_data['obstacle_type']}")
            print(f"    中心: ({obs_data['center']['x']:.2f}, {obs_data['center']['y']:.2f})")
            print(f"    尺寸: {obs_data['size']['width']:.2f} x {obs_data['size']['height']:.2f}")
            print(f"    时间: {obs_data['insert_time']:.2f}s + {obs_data['duration_minutes']:.2f}min")
        
        return True
        
    except Exception as e:
        print(f"障碍物加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_obstacle_timeframe_filtering():
    """测试时间范围过滤功能"""
    print("\n=== 测试时间范围过滤功能 ===")
    
    try:
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 测试不同时间范围
        time_ranges = [
            (0, 50),      # 前50秒
            (50, 100),    # 50-100秒
            (100, 150),   # 100-150秒
            (150, 200),   # 150-200秒
        ]
        
        for start_time, end_time in time_ranges:
            obstacles = predictor.data_generator.get_obstacles_in_timeframe(start_time, end_time)
            print(f"时间范围 {start_time}-{end_time}s: {len(obstacles)} 个障碍物")
            
            if obstacles:
                # 显示第一个障碍物的详细信息
                center_x, center_y, width, height, insert_time, duration = obstacles[0]
                print(f"  示例: 中心({center_x:.2f}, {center_y:.2f}), "
                      f"尺寸{width:.2f}x{height:.2f}, "
                      f"时间{insert_time:.2f}+{duration:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"时间范围过滤测试失败: {e}")
        return False


def test_sample_generation():
    """测试样本生成功能"""
    print("\n=== 测试样本生成功能 ===")
    
    try:
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 生成几个样本
        print("生成训练样本...")
        samples = []
        for i in range(5):
            sample = predictor.data_generator.generate_sample()
            samples.append(sample)
            
            print(f"样本 {i+1}:")
            print(f"  起点索引: {sample['start_point']}")
            print(f"  终点索引: {sample['end_point']}")
            print(f"  AGV速度: {sample['agv_speed']:.2f} m/s")
            print(f"  开始时间: {sample['start_time']:.2f}s")
            print(f"  距离: {sample['distance']:.2f}m")
            print(f"  实际时间: {sample['actual_time']:.2f}s")
            print(f"  图数据形状: 节点{sample['graph_data'].x.shape}, 边{sample['graph_data'].edge_index.shape}")
        
        # 统计分析
        actual_times = [s['actual_time'] for s in samples]
        distances = [s['distance'] for s in samples]
        speeds = [s['agv_speed'] for s in samples]
        
        print(f"\n样本统计:")
        print(f"  实际时间: {np.mean(actual_times):.2f} ± {np.std(actual_times):.2f}s")
        print(f"  距离: {np.mean(distances):.2f} ± {np.std(distances):.2f}m")
        print(f"  速度: {np.mean(speeds):.2f} ± {np.std(speeds):.2f} m/s")
        
        return True
        
    except Exception as e:
        print(f"样本生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_prediction_with_real_obstacles():
    """测试使用真实障碍物的预测功能"""
    print("\n=== 测试使用真实障碍物的预测功能 ===")
    
    try:
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 获取可用的点ID
        available_points = list(predictor.graph_structure['task_points'].keys())
        start_point = available_points[0]
        end_point = available_points[-1]
        
        print(f"测试路径: {start_point} -> {end_point}")
        
        # 测试不同时间点的预测
        test_times = [0, 50, 100, 150, 200]
        
        for start_time in test_times:
            # 获取该时间点相关的障碍物
            end_time = start_time + 60  # 假设运输需要60秒
            obstacles = predictor.data_generator.get_obstacles_in_timeframe(start_time, end_time)
            
            # 转换为预测器期望的格式（如果需要）
            # 这里我们直接使用内部方法进行预测
            start_idx = predictor.data_generator.point_to_idx[start_point]
            end_idx = predictor.data_generator.point_to_idx[end_point]
            
            actual_time = predictor.data_generator.calculate_actual_time(
                start_idx, end_idx, 1.0, start_time, obstacles
            )
            
            print(f"时间 {start_time}s: {len(obstacles)} 个相关障碍物, 预测时间: {actual_time:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"真实障碍物预测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def visualize_obstacles():
    """可视化障碍物分布"""
    print("\n=== 可视化障碍物分布 ===")
    
    try:
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 获取所有障碍物
        all_obstacles = predictor.data_generator.get_all_obstacles()
        
        if not all_obstacles:
            print("没有障碍物数据可视化")
            return True
        
        # 提取障碍物信息
        centers_x = [obs[0] for obs in all_obstacles]
        centers_y = [obs[1] for obs in all_obstacles]
        widths = [obs[2] for obs in all_obstacles]
        heights = [obs[3] for obs in all_obstacles]
        insert_times = [obs[4] for obs in all_obstacles]
        durations = [obs[5] for obs in all_obstacles]
        
        # 创建可视化
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 障碍物空间分布
        axes[0, 0].scatter(centers_x, centers_y, alpha=0.6, s=50)
        axes[0, 0].set_xlabel('X坐标')
        axes[0, 0].set_ylabel('Y坐标')
        axes[0, 0].set_title('障碍物空间分布')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 障碍物尺寸分布
        axes[0, 1].scatter(widths, heights, alpha=0.6, s=50)
        axes[0, 1].set_xlabel('宽度')
        axes[0, 1].set_ylabel('高度')
        axes[0, 1].set_title('障碍物尺寸分布')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 障碍物时间分布
        axes[1, 0].scatter(insert_times, durations, alpha=0.6, s=50)
        axes[1, 0].set_xlabel('插入时间 (s)')
        axes[1, 0].set_ylabel('持续时间 (s)')
        axes[1, 0].set_title('障碍物时间分布')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 障碍物时间线
        for i, (insert_time, duration) in enumerate(zip(insert_times, durations)):
            axes[1, 1].barh(i, duration, left=insert_time, height=0.8, alpha=0.6)
        
        axes[1, 1].set_xlabel('时间 (s)')
        axes[1, 1].set_ylabel('障碍物索引')
        axes[1, 1].set_title('障碍物时间线')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('obstacle_visualization.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print(f"障碍物可视化完成，共 {len(all_obstacles)} 个障碍物")
        print(f"空间范围: X({min(centers_x):.1f}, {max(centers_x):.1f}), Y({min(centers_y):.1f}, {max(centers_y):.1f})")
        print(f"时间范围: {min(insert_times):.1f} - {max(insert_times):.1f}s")
        print(f"持续时间: {min(durations):.1f} - {max(durations):.1f}s")
        
        return True
        
    except Exception as e:
        print(f"障碍物可视化失败: {e}")
        return False


def main():
    """主测试函数"""
    print("障碍物加载功能测试")
    print("=" * 50)
    
    tests = [
        ("障碍物加载", test_obstacle_loading),
        ("时间范围过滤", test_obstacle_timeframe_filtering),
        ("样本生成", test_sample_generation),
        ("真实障碍物预测", test_prediction_with_real_obstacles),
        ("障碍物可视化", visualize_obstacles),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ 测试 '{test_name}' 崩溃: {e}")
            results[test_name] = False
    
    # 总结结果
    print(f"\n{'='*50}")
    print("测试结果总结:")
    print(f"{'='*50}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<20} {status}")
    
    print(f"\n总体: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("❌ 部分测试失败！")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
