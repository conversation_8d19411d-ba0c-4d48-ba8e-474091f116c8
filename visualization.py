"""
可视化模块
负责可视化系统状态，包括AGV位置、任务状态、障碍物等
"""
import time

# 尝试导入matplotlib，如果不可用则使用替代方案
try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    import numpy as np
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    print("警告: matplotlib库未安装，可视化功能将被禁用。")
    print("如需可视化功能，请安装matplotlib: pip install matplotlib")
    MATPLOTLIB_AVAILABLE = False

class Visualizer:
    def __init__(self, mode="real-time"):
        """
        初始化可视化器

        Args:
            mode: 可视化模式，可选值：
                - "real-time": 实时可视化
                - "step": 逐步可视化
                - "final": 只显示最终结果
                - "none": 不进行可视化
        """
        self.mode = mode
        self.fig = None
        self.ax = None
        self.task_points_plot = {}
        self.channels_plot = {}
        self.agv_plots = {}
        self.obstacle_plots = {}
        self.task_plots = {}
        self.time_text = None

        # 颜色映射
        self.agv_colors = {
            'idle': 'green',
            'busy': 'blue',
            'charging': 'orange',
            'error': 'red'
        }

        self.task_colors = {
            'waiting': 'gray',
            'assigned': 'blue',
            'in_progress': 'orange',
            'completed': 'green'
        }

    def initialize(self, task_points, channels):
        """
        初始化可视化环境

        Args:
            task_points: 任务点字典
            channels: 通道字典
        """
        if self.mode == "none" or not MATPLOTLIB_AVAILABLE:
            return

        # 创建图形和坐标轴
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.ax.set_aspect('equal')
        self.ax.grid(True)

        # 设置坐标轴范围
        x_coords = [point['x'] for point in task_points.values()]
        y_coords = [point['y'] for point in task_points.values()]

        x_min, x_max = min(x_coords) - 1, max(x_coords) + 1
        y_min, y_max = min(y_coords) - 1, max(y_coords) + 1

        self.ax.set_xlim(x_min, x_max)
        self.ax.set_ylim(y_min, y_max)

        # 绘制任务点
        for point_id, point_info in task_points.items():
            x, y = point_info['x'], point_info['y']
            point = self.ax.plot(x, y, 'o', markersize=8, color='black')[0]
            self.ax.text(x, y + 0.2, point_id, ha='center', va='center')
            self.task_points_plot[point_id] = point

        # 绘制通道
        for (start, end), channel_info in channels.items():
            if start in task_points and end in task_points:
                start_x, start_y = task_points[start]['x'], task_points[start]['y']
                end_x, end_y = task_points[end]['x'], task_points[end]['y']

                line = self.ax.plot([start_x, end_x], [start_y, end_y], '-', color='gray', alpha=0.5)[0]
                self.channels_plot[(start, end)] = line

        # 添加时间文本
        self.time_text = self.ax.text(0.02, 0.98, 'Time: 0.00s', transform=self.ax.transAxes,
                                      fontsize=12, verticalalignment='top')

        # 添加图例
        legend_elements = [
            patches.Patch(facecolor='green', edgecolor='black', label='Idle AGV'),
            patches.Patch(facecolor='blue', edgecolor='black', label='Busy AGV'),
            patches.Patch(facecolor='orange', edgecolor='black', label='Charging AGV'),
            patches.Patch(facecolor='red', edgecolor='black', label='Error AGV'),
            patches.Patch(facecolor='gray', edgecolor='black', label='Waiting Task'),
            patches.Patch(facecolor='blue', edgecolor='black', label='Assigned Task'),
            patches.Patch(facecolor='orange', edgecolor='black', label='In Progress Task'),
            patches.Patch(facecolor='green', edgecolor='black', label='Completed Task'),
            patches.Patch(facecolor='red', edgecolor='black', alpha=0.5, label='Obstacle')
        ]

        self.ax.legend(handles=legend_elements, loc='upper right')

        # 设置标题
        self.ax.set_title('AGV System Simulation')

        # 显示图形
        if self.mode == "real-time":
            plt.ion()  # 开启交互模式
            plt.show()

    def visualize(self, current_time, agv_list, task_pool, obstacle_pool, task_points, channels):
        """
        可视化当前系统状态

        Args:
            current_time: 当前时间
            agv_list: AGV列表
            task_pool: 任务池
            obstacle_pool: 障碍物池
            task_points: 任务点字典
            channels: 通道字典
        """
        if self.mode == "none" or not MATPLOTLIB_AVAILABLE or self.fig is None:
            # 如果没有matplotlib或者不需要可视化，只打印简单的状态信息
            print(f"\r时间: {current_time:.2f}s | AGV: {len(agv_list)} | 任务: {len(task_pool)} | 障碍物: {len(obstacle_pool)}", end="")
            return

        # 更新时间文本
        self.time_text.set_text(f'Time: {current_time:.2f}s')

        # 更新AGV位置
        for agv in agv_list:
            # 获取AGV位置坐标
            if isinstance(agv.position, str) and agv.position in task_points:
                # 如果位置是任务点ID，获取任务点坐标
                x, y = task_points[agv.position]['x'], task_points[agv.position]['y']
                print(f"AGV {agv.id} 在任务点 {agv.position}, 坐标: ({x}, {y})")
            elif isinstance(agv.position, tuple) and len(agv.position) == 2:
                # 如果位置是坐标，直接使用
                x, y = agv.position
                print(f"AGV {agv.id} 在坐标: ({x}, {y})")
            else:
                # 默认位置
                print(f"AGV {agv.id} 位置格式不正确: {agv.position}, 类型: {type(agv.position)}")
                x, y = 10, 10  # 使用中心点作为默认位置

            # 获取AGV颜色
            color = self.agv_colors.get(agv.status, 'black')

            # 如果AGV已经在图上，更新位置
            if agv.id in self.agv_plots:
                self.agv_plots[agv.id].set_data([x], [y])  # 使用列表形式
                self.agv_plots[agv.id].set_color(color)
            else:
                # 否则，创建新的AGV图形
                agv_plot = self.ax.plot([x], [y], 'o', markersize=10, color=color)[0]  # 使用列表形式
                self.agv_plots[agv.id] = agv_plot

                # 添加AGV ID标签
                self.ax.text(x, y - 0.3, agv.id, ha='center', va='center', fontsize=8)

        # 更新障碍物
        # 先清除所有旧的障碍物
        for obstacle_plot in self.obstacle_plots.values():
            obstacle_plot.remove()
        self.obstacle_plots = {}

        # 绘制当前活跃的障碍物
        for i, obstacle in enumerate(obstacle_pool):
            if obstacle.is_active(current_time):
                # 获取障碍物坐标
                top_left = obstacle.top_left
                bottom_right = obstacle.bottom_right

                width = bottom_right[0] - top_left[0]
                height = bottom_right[1] - top_left[1]

                # 创建矩形表示障碍物
                rect = patches.Rectangle(
                    top_left, width, height,
                    linewidth=1, edgecolor='black', facecolor='red', alpha=0.5
                )

                self.ax.add_patch(rect)
                self.obstacle_plots[f"obstacle_{i}"] = rect

        # 更新任务
        # 先清除所有旧的任务标记
        for task_plot in self.task_plots.values():
            if isinstance(task_plot, list):
                for p in task_plot:
                    if p:
                        p.remove()
            elif task_plot:
                task_plot.remove()
        self.task_plots = {}

        # 绘制当前任务
        for task in task_pool:
            if task.start_point in task_points and task.end_point in task_points:
                start_x, start_y = task_points[task.start_point]['x'], task_points[task.start_point]['y']
                end_x, end_y = task_points[task.end_point]['x'], task_points[task.end_point]['y']

                # 获取任务颜色
                color = self.task_colors.get(task.status, 'black')

                # 绘制任务起点和终点之间的箭头
                arrow = self.ax.annotate(
                    '', xy=(end_x, end_y), xytext=(start_x, start_y),
                    arrowprops=dict(arrowstyle='->', color=color, lw=2, alpha=0.7)
                )

                # 在起点和终点附近显示任务ID
                text = self.ax.text(
                    (start_x + end_x) / 2, (start_y + end_y) / 2,
                    task.id, ha='center', va='center', fontsize=8,
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.7)
                )

                self.task_plots[task.id] = [arrow, text]

        # 绘制AGV的路径
        for agv in agv_list:
            if agv.path and len(agv.path) > 1:
                path_x = []
                path_y = []

                for point_id in agv.path:
                    if point_id in task_points:
                        path_x.append(task_points[point_id]['x'])
                        path_y.append(task_points[point_id]['y'])

                if path_x and path_y:
                    # 如果AGV的路径已经在图上，更新路径
                    if f"{agv.id}_path" in self.task_plots:
                        self.task_plots[f"{agv.id}_path"].set_data(path_x, path_y)
                    else:
                        # 否则，创建新的路径图形
                        path_plot = self.ax.plot(
                            path_x, path_y, '--', color=self.agv_colors.get(agv.status, 'black'),
                            alpha=0.5, linewidth=1
                        )[0]
                        self.task_plots[f"{agv.id}_path"] = path_plot

        # 刷新图形
        if self.mode == "real-time":
            self.fig.canvas.draw_idle()
            self.fig.canvas.flush_events()
            plt.pause(0.01)
        elif self.mode == "step":
            plt.pause(0.5)

    def save_figure(self, filename):
        """
        保存当前图形

        Args:
            filename: 文件名
        """
        if not MATPLOTLIB_AVAILABLE or self.fig is None:
            print(f"无法保存图形，因为matplotlib库未安装或图形未初始化")
            return

        self.fig.savefig(filename, dpi=300, bbox_inches='tight')

    def close(self):
        """关闭可视化窗口"""
        if not MATPLOTLIB_AVAILABLE:
            return

        if self.fig:
            plt.close(self.fig)
            self.fig = None
            self.ax = None
