"""
可视化模块
负责可视化系统状态，包括AGV位置、任务状态、障碍物等
"""
import time

# 尝试导入matplotlib，如果不可用则使用替代方案
try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    import numpy as np
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    print("警告: matplotlib库未安装，可视化功能将被禁用。")
    print("如需可视化功能，请安装matplotlib: pip install matplotlib")
    MATPLOTLIB_AVAILABLE = False

class Visualizer:
    def __init__(self, mode="fast", speed_factor=1000.0):
        """
        初始化可视化器

        Args:
            mode: 可视化模式，可选值：
                - "real-time": 实时可视化
                - "step": 逐步可视化
                - "fast": 快速模式，不进行pause
                - "final": 只显示最终结果
                - "none": 不进行可视化
            speed_factor: 仿真速度倍数，1.0为实时，>1.0为加速，<1.0为减速
        """
        self.mode = mode
        self.speed_factor = speed_factor
        self.fig = None
        self.ax = None
        self.task_points_plot = {}
        self.channels_plot = {}
        self.agv_plots = {}
        self.obstacle_plots = {}
        self.task_plots = {}
        self.time_text = None

        # 计算可视化更新间隔
        if mode == "fast":
            # 快速模式：更大的更新间隔，减少刷新频率
            self.update_interval = max(0.1, 1.0 / speed_factor)
        else:
            # 其他模式：正常更新间隔
            self.update_interval = max(0.001, 0.1 / speed_factor)  # 最小间隔1ms
        self.last_update_time = 0

        # 颜色映射
        self.agv_colors = {
            'idle': 'green',
            'busy': 'blue',
            'charging': 'orange',
            'error': 'red'
        }

        self.task_colors = {
            'waiting': 'gray',
            'assigned': 'blue',
            'in_progress': 'orange',
            'completed': 'green'
        }

    def initialize(self, task_points, channels):
        """
        初始化可视化环境

        Args:
            task_points: 任务点字典
            channels: 通道字典
        """
        if self.mode == "none" or not MATPLOTLIB_AVAILABLE:
            return

        # 创建图形和坐标轴，使用子图布局：左侧地图，右侧信息面板分两列
        self.fig = plt.figure(figsize=(24, 12))

        # 创建网格布局：左侧地图占60%，右侧信息面板占40%（分为两列）
        gs = self.fig.add_gridspec(1, 4, width_ratios=[3, 1, 1, 0], hspace=0.1, wspace=0.15)

        # 地图区域
        self.ax = self.fig.add_subplot(gs[0, 0])
        self.ax.set_aspect('equal')
        self.ax.grid(True, alpha=0.3)

        # 左侧信息面板区域
        self.info_ax_left = self.fig.add_subplot(gs[0, 1])
        self.info_ax_left.set_xlim(0, 1)
        self.info_ax_left.set_ylim(0, 1)
        self.info_ax_left.axis('off')  # 隐藏坐标轴

        # 右侧信息面板区域
        self.info_ax_right = self.fig.add_subplot(gs[0, 2])
        self.info_ax_right.set_xlim(0, 1)
        self.info_ax_right.set_ylim(0, 1)
        self.info_ax_right.axis('off')  # 隐藏坐标轴

        # 设置坐标轴范围
        x_coords = [point['x'] for point in task_points.values()]
        y_coords = [point['y'] for point in task_points.values()]

        x_min, x_max = min(x_coords) - 1, max(x_coords) + 1
        y_min, y_max = min(y_coords) - 1, max(y_coords) + 1

        self.ax.set_xlim(x_min, x_max)
        self.ax.set_ylim(y_min, y_max)

        # 绘制任务点
        for point_id, point_info in task_points.items():
            x, y = point_info['x'], point_info['y']
            point = self.ax.plot(x, y, 'o', markersize=8, color='black')[0]
            self.ax.text(x, y + 0.2, point_id, ha='center', va='center')
            self.task_points_plot[point_id] = point

        # 绘制通道
        for (start, end), channel_info in channels.items():
            if start in task_points and end in task_points:
                start_x, start_y = task_points[start]['x'], task_points[start]['y']
                end_x, end_y = task_points[end]['x'], task_points[end]['y']

                line = self.ax.plot([start_x, end_x], [start_y, end_y], '-', color='gray', alpha=0.5)[0]
                self.channels_plot[(start, end)] = line

        # 在信息面板中创建信息显示区域
        # 时间信息（跨越两列，居中显示）
        self.time_text = self.info_ax_left.text(1.0, 0.95, 'Time: 0.00s', transform=self.info_ax_left.transAxes,
                                               fontsize=14, fontweight='bold', verticalalignment='top',
                                               horizontalalignment='center')

        # 添加列标题
        self.info_ax_left.text(0.5, 0.88, 'SYSTEM & TASKS', transform=self.info_ax_left.transAxes,
                              fontsize=12, fontweight='bold', verticalalignment='top',
                              horizontalalignment='center', color='darkblue')

        self.info_ax_right.text(0.5, 0.88, 'AGV & PATHS', transform=self.info_ax_right.transAxes,
                               fontsize=12, fontweight='bold', verticalalignment='top',
                               horizontalalignment='center', color='darkgreen')

        # 创建信息表格的标题和内容区域 - 分为两列
        self.info_texts = {
            # 左列：系统统计和任务池
            'system_stats': self.info_ax_left.text(0.05, 0.80, '', transform=self.info_ax_left.transAxes,
                                                  fontsize=9, verticalalignment='top', fontfamily='monospace'),
            'task_pool': self.info_ax_left.text(0.05, 0.40, '', transform=self.info_ax_left.transAxes,
                                              fontsize=9, verticalalignment='top', fontfamily='monospace'),

            # 右列：AGV状态和路径规划
            'agv_status': self.info_ax_right.text(0.05, 0.80, '', transform=self.info_ax_right.transAxes,
                                                fontsize=9, verticalalignment='top', fontfamily='monospace'),
            'path_info': self.info_ax_right.text(0.05, 0.40, '', transform=self.info_ax_right.transAxes,
                                               fontsize=9, verticalalignment='top', fontfamily='monospace')
        }

        # 添加图例
        legend_elements = [
            patches.Patch(facecolor='green', edgecolor='black', label='Idle AGV'),
            patches.Patch(facecolor='blue', edgecolor='black', label='Busy AGV'),
            patches.Patch(facecolor='orange', edgecolor='black', label='Charging AGV'),
            patches.Patch(facecolor='red', edgecolor='black', label='Error AGV'),
            patches.Patch(facecolor='gray', edgecolor='black', label='Waiting Task'),
            patches.Patch(facecolor='blue', edgecolor='black', label='Assigned Task'),
            patches.Patch(facecolor='orange', edgecolor='black', label='In Progress Task'),
            patches.Patch(facecolor='green', edgecolor='black', label='Completed Task'),
            patches.Patch(facecolor='red', edgecolor='black', alpha=0.5, label='Obstacle')
        ]

        self.ax.legend(handles=legend_elements, loc='upper center', bbox_to_anchor=(0.5, -0.05), ncol=3)

        # 设置标题
        self.ax.set_title(f'AGV System Simulation (Speed: {self.speed_factor}x)')

        # 显示图形
        if self.mode == "real-time":
            plt.ion()  # 开启交互模式
            plt.show()

    def visualize(self, current_time, agv_list, task_pool, obstacle_pool, task_points, channels):
        """
        可视化当前系统状态

        Args:
            current_time: 当前时间
            agv_list: AGV列表
            task_pool: 任务池
            obstacle_pool: 障碍物池
            task_points: 任务点字典
            channels: 通道字典
        """
        if self.mode == "none" or not MATPLOTLIB_AVAILABLE or self.fig is None:
            # 如果没有matplotlib或者不需要可视化，只打印简单的状态信息
            if current_time - self.last_update_time >= 1.0:  # 每秒更新一次文本输出
                print(f"\r时间: {current_time:.2f}s | AGV: {len(agv_list)} | 任务: {len(task_pool)} | 障碍物: {len(obstacle_pool)}", end="")
                self.last_update_time = current_time
            return

        # 检查是否需要更新可视化（根据速度因子控制更新频率）
        if current_time - self.last_update_time < self.update_interval:
            return

        self.last_update_time = current_time

        # 更新时间文本
        self.time_text.set_text(f'Time: {current_time:.2f}s (Speed: {self.speed_factor}x)')

        # 更新信息面板
        self._update_info_panel(current_time, agv_list, task_pool, obstacle_pool)

        # 更新AGV位置
        for agv in agv_list:
            # 获取AGV位置坐标
            if isinstance(agv.position, str) and agv.position in task_points:
                # 如果位置是任务点ID，获取任务点坐标
                x, y = task_points[agv.position]['x'], task_points[agv.position]['y']
            elif isinstance(agv.position, tuple) and len(agv.position) == 2:
                # 如果位置是坐标，直接使用
                x, y = agv.position
            else:
                # 默认位置
                x, y = 10, 10  # 使用中心点作为默认位置

            # 获取AGV颜色
            color = self.agv_colors.get(agv.status, 'black')

            # 如果AGV已经在图上，更新位置
            if agv.id in self.agv_plots:
                self.agv_plots[agv.id].set_data([x], [y])  # 使用列表形式
                self.agv_plots[agv.id].set_color(color)
            else:
                # 否则，创建新的AGV图形
                agv_plot = self.ax.plot([x], [y], 'o', markersize=10, color=color)[0]  # 使用列表形式
                self.agv_plots[agv.id] = agv_plot

                # 添加AGV ID标签
                self.ax.text(x, y - 0.3, agv.id, ha='center', va='center', fontsize=8)

        # 更新障碍物
        # 先清除所有旧的障碍物
        for obstacle_plot in self.obstacle_plots.values():
            obstacle_plot.remove()
        self.obstacle_plots = {}

        # 绘制当前活跃的障碍物
        for i, obstacle in enumerate(obstacle_pool):
            if obstacle.is_active(current_time):
                # 获取障碍物坐标
                top_left = obstacle.top_left
                bottom_right = obstacle.bottom_right

                width = bottom_right[0] - top_left[0]
                height = bottom_right[1] - top_left[1]

                # 创建矩形表示障碍物
                rect = patches.Rectangle(
                    top_left, width, height,
                    linewidth=1, edgecolor='black', facecolor='red', alpha=0.5
                )

                self.ax.add_patch(rect)
                self.obstacle_plots[f"obstacle_{i}"] = rect

        # 更新任务
        # 先清除所有旧的任务标记
        for task_plot in self.task_plots.values():
            if isinstance(task_plot, list):
                for p in task_plot:
                    if p:
                        p.remove()
            elif task_plot:
                task_plot.remove()
        self.task_plots = {}

        # 绘制当前任务
        for task in task_pool:
            if task.start_point in task_points and task.end_point in task_points:
                start_x, start_y = task_points[task.start_point]['x'], task_points[task.start_point]['y']
                end_x, end_y = task_points[task.end_point]['x'], task_points[task.end_point]['y']

                # 获取任务颜色
                color = self.task_colors.get(task.status, 'black')

                # 绘制任务起点和终点之间的箭头
                arrow = self.ax.annotate(
                    '', xy=(end_x, end_y), xytext=(start_x, start_y),
                    arrowprops=dict(arrowstyle='->', color=color, lw=2, alpha=0.7)
                )

                # 在起点和终点附近显示任务ID
                text = self.ax.text(
                    (start_x + end_x) / 2, (start_y + end_y) / 2,
                    task.id, ha='center', va='center', fontsize=8,
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.7)
                )

                self.task_plots[task.id] = [arrow, text]

        # 绘制AGV的路径
        for agv in agv_list:
            if agv.path and len(agv.path) > 1:
                path_x = []
                path_y = []

                for point_id in agv.path:
                    if point_id in task_points:
                        path_x.append(task_points[point_id]['x'])
                        path_y.append(task_points[point_id]['y'])

                if path_x and path_y:
                    # 如果AGV的路径已经在图上，更新路径
                    if f"{agv.id}_path" in self.task_plots:
                        self.task_plots[f"{agv.id}_path"].set_data(path_x, path_y)
                    else:
                        # 否则，创建新的路径图形
                        path_plot = self.ax.plot(
                            path_x, path_y, '--', color=self.agv_colors.get(agv.status, 'black'),
                            alpha=0.5, linewidth=1
                        )[0]
                        self.task_plots[f"{agv.id}_path"] = path_plot

        # 刷新图形
        if self.mode == "real-time":
            self.fig.canvas.draw_idle()
            self.fig.canvas.flush_events()
            # 根据速度因子调整暂停时间
            plt.pause(max(0.001, self.update_interval))
        elif self.mode == "fast":
            # 快速模式：只刷新画布，不进行pause
            self.fig.canvas.draw_idle()
            self.fig.canvas.flush_events()
            # 不进行pause，让仿真尽可能快地运行
        elif self.mode == "step":
            plt.pause(max(0.1, 0.5 / self.speed_factor))

    def save_figure(self, filename):
        """
        保存当前图形

        Args:
            filename: 文件名
        """
        if not MATPLOTLIB_AVAILABLE or self.fig is None:
            print(f"无法保存图形，因为matplotlib库未安装或图形未初始化")
            return

        self.fig.savefig(filename, dpi=300, bbox_inches='tight')

    def _update_info_panel(self, current_time, agv_list, task_pool, obstacle_pool):
        """更新右侧信息面板"""
        if not hasattr(self, 'info_texts'):
            return

        # 1. 系统统计信息
        total_agvs = len(agv_list)
        total_tasks = len(task_pool)
        total_obstacles = len(obstacle_pool)

        # 计算完成的任务数
        completed_tasks = 0
        for agv in agv_list:
            if hasattr(agv, 'completed_tasks'):
                completed_tasks += len(agv.completed_tasks)

        # 计算AGV利用率
        busy_agvs = sum(1 for agv in agv_list if getattr(agv, 'status', 'idle') == 'busy')
        utilization = (busy_agvs / total_agvs * 100) if total_agvs > 0 else 0

        system_info = [
            "=== SYSTEM STATS ===",
            f"Runtime: {current_time:.1f}s",
            f"AGVs: {total_agvs}",
            f"Tasks: {total_tasks}",
            f"Obstacles: {total_obstacles}",
            f"Completed: {completed_tasks}",
            f"Utilization: {utilization:.1f}%"
        ]
        self.info_texts['system_stats'].set_text('\n'.join(system_info))

        # 2. 任务池信息
        task_stats = {'waiting': 0, 'assigned': 0, 'in_progress': 0, 'completed': 0}
        task_types = {}

        for task in task_pool:
            status = getattr(task, 'status', 'waiting')
            task_stats[status] = task_stats.get(status, 0) + 1

            task_type = getattr(task, 'task_type', 'unknown')
            task_types[task_type] = task_types.get(task_type, 0) + 1

        task_info = [
            "=== TASK POOL ===",
            f"Total: {len(task_pool)}",
            f"Waiting: {task_stats['waiting']:>3}",
            f"Assigned: {task_stats['assigned']:>3}",
            f"In Progress: {task_stats['in_progress']:>3}",
            f"Completed: {task_stats['completed']:>3}",
            "",
            "--- TASK TYPES ---"
        ]

        # 添加任务类型统计（最多显示5种）
        type_items = list(task_types.items())[:5]
        for task_type, count in type_items:
            task_info.append(f"{task_type}: {count:>3}")

        if len(task_types) > 5:
            task_info.append(f"... +{len(task_types)-5} more")

        self.info_texts['task_pool'].set_text('\n'.join(task_info))

        # 3. AGV状态信息
        agv_stats = {'idle': 0, 'busy': 0, 'charging': 0, 'error': 0}
        agv_tasks = []

        for agv in agv_list:
            status = getattr(agv, 'status', 'idle')
            agv_stats[status] = agv_stats.get(status, 0) + 1

            if hasattr(agv, 'current_task') and agv.current_task:
                task_info_line = f"{agv.id}: {agv.current_task.id}"
                if hasattr(agv.current_task, 'start_point') and hasattr(agv.current_task, 'end_point'):
                    start = agv.current_task.start_point
                    end = agv.current_task.end_point
                    task_info_line = f"{agv.id}: {start}->{end}"
                agv_tasks.append(task_info_line)

        agv_info = [
            "=== AGV STATUS ===",
            f"Idle: {agv_stats['idle']:>3}",
            f"Busy: {agv_stats['busy']:>3}",
            f"Charging: {agv_stats['charging']:>3}",
            f"Error: {agv_stats['error']:>3}",
            "",
            "--- ASSIGNMENTS ---"
        ]

        # 添加任务分配详情（最多显示6个）
        if agv_tasks:
            for task_assignment in agv_tasks[:6]:
                agv_info.append(task_assignment)
            if len(agv_tasks) > 6:
                agv_info.append(f"... +{len(agv_tasks)-6} more")
        else:
            agv_info.append("No assignments")

        self.info_texts['agv_status'].set_text('\n'.join(agv_info))

        # 4. 路径规划信息
        path_stats = {'with_path': 0, 'no_path': 0}
        path_details = []

        for agv in agv_list:
            if hasattr(agv, 'path') and agv.path and len(agv.path) > 1:
                path_stats['with_path'] += 1
                path_length = len(agv.path)
                current_index = getattr(agv, 'path_index', 0)
                progress_percent = (current_index / path_length * 100) if path_length > 0 else 0
                path_details.append(f"{agv.id}: {current_index}/{path_length} ({progress_percent:.0f}%)")
            else:
                path_stats['no_path'] += 1

        path_info = [
            "=== PATH PLANNING ===",
            f"With Path: {path_stats['with_path']:>3}",
            f"No Path: {path_stats['no_path']:>3}",
            "",
            "--- PROGRESS ---"
        ]

        # 添加路径进度详情（最多显示5个）
        if path_details:
            for path_detail in path_details[:5]:
                path_info.append(path_detail)
            if len(path_details) > 5:
                path_info.append(f"... +{len(path_details)-5} more")
        else:
            path_info.append("No active paths")

        self.info_texts['path_info'].set_text('\n'.join(path_info))



    def close(self):
        """关闭可视化窗口"""
        if not MATPLOTLIB_AVAILABLE:
            return

        if self.fig:
            plt.close(self.fig)
            self.fig = None
            self.ax = None
