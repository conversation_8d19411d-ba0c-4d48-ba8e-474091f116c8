#!/usr/bin/env python3
"""
Test script for multi-CASE training and simulation switching
多CASE训练和仿真切换测试脚本
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from transport_time_predictor import TransportTimePredictorInterface
from collections import defaultdict
import time


def test_multi_case_data_generation():
    """测试多CASE数据生成"""
    print("=== Testing Multi-CASE Data Generation ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 测试多CASE数据生成
        case_numbers = [1]  # 目前只有CASE1，可以扩展
        num_samples = 1000
        
        print(f"Generating {num_samples} samples from CASEs: {case_numbers}")
        
        start_time = time.time()
        dataset = predictor.data_generator.generate_dataset(num_samples, case_numbers)
        generation_time = time.time() - start_time
        
        print(f"✓ Generated {len(dataset)} samples in {generation_time:.2f}s")
        
        # 分析CASE分布
        case_counts = defaultdict(int)
        for sample in dataset:
            case_num = sample.get('case_number', 'unknown')
            case_counts[case_num] += 1
        
        print("CASE distribution in dataset:")
        for case_num, count in case_counts.items():
            print(f"  CASE{case_num}: {count} samples ({count/len(dataset)*100:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"✗ Multi-CASE data generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_multi_case_training():
    """测试多CASE训练"""
    print("\n=== Testing Multi-CASE Training ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 使用调试配置进行快速训练
        predictor.config.update({
            'num_epochs': 3,
            'batch_size': 16,
            'graph_hidden_dim': 32,
            'temporal_hidden_dim': 64
        })
        
        # 重新创建模型以使用新配置
        from transport_time_predictor import TransportTimePredictor
        predictor.model = TransportTimePredictor(predictor.config)
        predictor.model.to(predictor.device)
        
        # 多CASE训练
        case_numbers = [1]  # 目前只有CASE1
        
        print(f"Starting multi-CASE training with CASEs: {case_numbers}")
        start_time = time.time()
        
        predictor.train_model(
            num_train_samples=200,
            num_val_samples=50,
            train_case_numbers=case_numbers,
            val_case_numbers=case_numbers
        )
        
        training_time = time.time() - start_time
        print(f"✓ Multi-CASE training completed in {training_time:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"✗ Multi-CASE training failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_case_switching():
    """测试CASE切换功能"""
    print("\n=== Testing CASE Switching ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 获取初始CASE信息
        initial_info = predictor.get_case_info()
        print(f"Initial CASE info: {initial_info}")
        
        # 测试预测
        pred_time_1 = predictor.predict_transport_time(
            start_point='P1',
            end_point='P20',
            agv_speed=1.0,
            start_time=0.0
        )
        print(f"Prediction with CASE{predictor.case_number}: {pred_time_1:.2f}s")
        
        # 测试仿真预测接口
        pred_time_sim = predictor.predict_for_simulation(
            start_point='P1',
            end_point='P20',
            agv_speed=1.0,
            start_time=0.0,
            current_case=1  # 保持相同CASE
        )
        print(f"Simulation prediction: {pred_time_sim:.2f}s")
        
        # 验证预测一致性
        if abs(pred_time_1 - pred_time_sim) < 1e-6:
            print("✓ Prediction consistency maintained")
        else:
            print(f"⚠ Prediction difference: {abs(pred_time_1 - pred_time_sim):.6f}")
        
        return True
        
    except Exception as e:
        print(f"✗ CASE switching test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_multi_case_training_interface():
    """测试多CASE训练接口"""
    print("\n=== Testing Multi-CASE Training Interface ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 使用调试配置
        predictor.config.update({
            'num_epochs': 2,
            'batch_size': 8,
            'graph_hidden_dim': 16,
            'temporal_hidden_dim': 32
        })
        
        # 重新创建模型
        from transport_time_predictor import TransportTimePredictor
        predictor.model = TransportTimePredictor(predictor.config)
        predictor.model.to(predictor.device)
        
        # 测试多CASE联合训练接口
        case_numbers = [1]  # 目前只有CASE1
        
        print(f"Testing multi-CASE training interface with CASEs: {case_numbers}")
        start_time = time.time()
        
        predictor.train_multi_case_model(
            case_numbers=case_numbers,
            samples_per_case=100,
            val_ratio=0.2
        )
        
        training_time = time.time() - start_time
        print(f"✓ Multi-CASE training interface completed in {training_time:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"✗ Multi-CASE training interface failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_simulation_workflow():
    """测试仿真工作流程"""
    print("\n=== Testing Simulation Workflow ===")
    
    try:
        # 创建预测器并训练
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 快速训练
        predictor.config.update({
            'num_epochs': 2,
            'batch_size': 8
        })
        
        from transport_time_predictor import TransportTimePredictor
        predictor.model = TransportTimePredictor(predictor.config)
        predictor.model.to(predictor.device)
        
        print("Quick training for simulation test...")
        predictor.train_model(num_train_samples=100, num_val_samples=25)
        
        # 模拟仿真工作流程
        simulation_cases = [1, 1, 1]  # 模拟仿真中的CASE切换
        test_scenarios = [
            ('P1', 'P10', 1.0, 0.0),
            ('P5', 'P15', 1.5, 10.0),
            ('P2', 'P18', 1.0, 20.0)
        ]
        
        print("\nSimulating workflow with CASE switching:")
        for i, (case_num, (start, end, speed, time)) in enumerate(zip(simulation_cases, test_scenarios)):
            print(f"  Step {i+1}: CASE{case_num}, {start}->{end}")
            
            pred_time = predictor.predict_for_simulation(
                start_point=start,
                end_point=end,
                agv_speed=speed,
                start_time=time,
                current_case=case_num
            )
            
            print(f"    Predicted time: {pred_time:.2f}s")
        
        print("✓ Simulation workflow completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Simulation workflow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def visualize_multi_case_training():
    """可视化多CASE训练效果"""
    print("\n=== Visualizing Multi-CASE Training ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 生成不同CASE的样本进行对比
        case_numbers = [1]  # 目前只有CASE1
        samples_per_case = 500
        
        all_samples = []
        case_labels = []
        
        for case_num in case_numbers:
            print(f"Generating samples for CASE{case_num}...")
            samples = predictor.data_generator._generate_case_specific_dataset(case_num, samples_per_case)
            all_samples.extend(samples)
            case_labels.extend([f"CASE{case_num}"] * len(samples))
        
        # 提取特征进行可视化
        transport_times = [sample['actual_time'] for sample in all_samples]
        distances = [sample['distance'] for sample in all_samples]
        speeds = [sample['agv_speed'] for sample in all_samples]
        
        # 创建可视化
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 运输时间分布
        axes[0, 0].hist(transport_times, bins=30, alpha=0.7, color='blue')
        axes[0, 0].set_xlabel('Transport Time (s)')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].set_title('Transport Time Distribution')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 距离分布
        axes[0, 1].hist(distances, bins=30, alpha=0.7, color='green')
        axes[0, 1].set_xlabel('Distance (m)')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].set_title('Distance Distribution')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 速度分布
        unique_speeds = list(set(speeds))
        speed_counts = [speeds.count(speed) for speed in unique_speeds]
        axes[1, 0].bar(unique_speeds, speed_counts, alpha=0.7, color='red')
        axes[1, 0].set_xlabel('AGV Speed (m/s)')
        axes[1, 0].set_ylabel('Count')
        axes[1, 0].set_title('AGV Speed Distribution')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 时间vs距离散点图
        colors = ['blue' if label == 'CASE1' else 'red' for label in case_labels]
        axes[1, 1].scatter(distances, transport_times, c=colors, alpha=0.6, s=20)
        axes[1, 1].set_xlabel('Distance (m)')
        axes[1, 1].set_ylabel('Transport Time (s)')
        axes[1, 1].set_title('Transport Time vs Distance')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('multi_case_training_analysis.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print("✓ Multi-CASE training visualization saved as 'multi_case_training_analysis.png'")
        return True
        
    except Exception as e:
        print(f"✗ Multi-CASE training visualization failed: {e}")
        return False


def main():
    """主测试函数"""
    print("Multi-CASE Training and Simulation Test")
    print("=" * 50)
    
    tests = [
        ("Multi-CASE Data Generation", test_multi_case_data_generation),
        ("Multi-CASE Training", test_multi_case_training),
        ("CASE Switching", test_case_switching),
        ("Multi-CASE Training Interface", test_multi_case_training_interface),
        ("Simulation Workflow", test_simulation_workflow),
        ("Multi-CASE Training Visualization", visualize_multi_case_training),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ Test '{test_name}' crashed: {e}")
            results[test_name] = False
    
    # 总结结果
    print(f"\n{'='*50}")
    print("Test Results Summary:")
    print(f"{'='*50}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<35} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
