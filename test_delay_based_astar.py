#!/usr/bin/env python3
"""
Test script for delay-based A* algorithm
测试基于延迟调整的A*算法
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from transport_time_predictor import TransportTimePredictorInterface
import time


def test_delay_calculation():
    """测试延迟计算功能"""
    print("=== Testing Delay Calculation ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        data_generator = predictor.data_generator
        
        # 测试路径延迟计算
        start_pos = {'x': 0.0, 'y': 0.0}
        end_pos = {'x': 10.0, 'y': 10.0}
        
        # 创建一个会影响路径的障碍物
        obstacles = [
            (5.0, 5.0, 4.0, 4.0, 2.0, 5.0)  # 中心(5,5), 4x4大小, 2s开始, 持续5s
        ]
        start_time = 0.0
        
        # 计算延迟
        delay = data_generator._calculate_path_delay(start_pos, end_pos, obstacles, start_time)
        
        print(f"Path delay calculation:")
        print(f"  Start: ({start_pos['x']}, {start_pos['y']})")
        print(f"  End: ({end_pos['x']}, {end_pos['y']})")
        print(f"  Obstacle: center=(5,5), size=4x4, time=2-7s")
        print(f"  Start time: {start_time}s")
        print(f"  Calculated delay: {delay:.4f}s")
        
        if delay >= 0:
            print("✓ Delay calculation completed successfully")
            return True
        else:
            print("⚠ Negative delay calculated")
            return False
        
    except Exception as e:
        print(f"✗ Delay calculation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_astar_with_delays():
    """测试带延迟的A*算法"""
    print("\n=== Testing A* with Delays ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        data_generator = predictor.data_generator
        
        task_points = data_generator.task_points
        
        if len(task_points) >= 2:
            point_ids = list(task_points.keys())
            start_point_id = point_ids[0]
            end_point_id = point_ids[-1]
            
            start_pos = task_points[start_point_id]
            end_pos = task_points[end_point_id]
            
            print(f"Testing A* with delays:")
            print(f"  From {start_point_id} to {end_point_id}")
            print(f"  Start: ({start_pos['x']:.2f}, {start_pos['y']:.2f})")
            print(f"  End: ({end_pos['x']:.2f}, {end_pos['y']:.2f})")
            
            # 无障碍物的情况
            no_obstacles = []
            start_time = 0.0
            
            clear_time = data_generator._astar_pathfinding(
                start_pos, end_pos, no_obstacles, start_time
            )
            
            # 有障碍物的情况
            obstacles = [
                (15.0, 15.0, 6.0, 6.0, 1.0, 8.0),  # 障碍物1
                (25.0, 25.0, 4.0, 4.0, 3.0, 6.0)   # 障碍物2
            ]
            
            delayed_time = data_generator._astar_pathfinding(
                start_pos, end_pos, obstacles, start_time
            )
            
            print(f"  Clear path time: {clear_time:.4f}s")
            print(f"  With obstacles time: {delayed_time:.4f}s")
            
            if clear_time is not None and delayed_time is not None:
                if delayed_time >= clear_time:
                    print("✓ A* correctly accounts for obstacle delays")
                else:
                    print("⚠ Delayed time less than clear time (might be valid)")
                return True
            else:
                print("⚠ A* pathfinding failed")
                return False
        else:
            print("⚠ Not enough task points for testing")
            return True
        
    except Exception as e:
        print(f"✗ A* with delays test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_actual_time_calculation():
    """测试实际时间计算"""
    print("\n=== Testing Actual Time Calculation ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 生成样本并分析时间计算
        num_samples = 25
        base_times = []
        actual_times = []
        delays = []
        
        for _ in range(num_samples):
            sample = predictor.data_generator.generate_sample()
            
            distance = sample['distance']
            agv_speed = sample['agv_speed']
            base_time = distance / agv_speed
            actual_time = sample['actual_time']
            delay = actual_time - base_time
            
            base_times.append(base_time)
            actual_times.append(actual_time)
            delays.append(delay)
        
        print(f"Actual time calculation analysis ({num_samples} samples):")
        print(f"  Average base time: {np.mean(base_times):.4f}s")
        print(f"  Average actual time: {np.mean(actual_times):.4f}s")
        print(f"  Average delay: {np.mean(delays):.4f}s")
        print(f"  Max delay: {np.max(delays):.4f}s")
        print(f"  Samples with delay > 0.1s: {sum(1 for d in delays if d > 0.1)}/{num_samples}")
        
        # 验证时间计算的合理性
        valid_times = all(actual >= base - 0.01 for actual, base in zip(actual_times, base_times))
        if valid_times:
            print("✓ All actual times >= base times")
        else:
            print("⚠ Some actual times < base times")
        
        # 检查延迟的合理性
        reasonable_delays = all(d >= -0.01 for d in delays)
        if reasonable_delays:
            print("✓ All delays are non-negative")
        else:
            print("⚠ Some delays are negative")
        
        return True
        
    except Exception as e:
        print(f"✗ Actual time calculation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_different_obstacle_scenarios():
    """测试不同障碍物场景"""
    print("\n=== Testing Different Obstacle Scenarios ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        data_generator = predictor.data_generator
        
        task_points = data_generator.task_points
        
        if len(task_points) >= 2:
            point_ids = list(task_points.keys())
            start_pos = task_points[point_ids[0]]
            end_pos = task_points[point_ids[-1]]
            start_time = 0.0
            
            scenarios = [
                {
                    'name': 'No obstacles',
                    'obstacles': []
                },
                {
                    'name': 'Early obstacle (before arrival)',
                    'obstacles': [(10.0, 10.0, 4.0, 4.0, 0.5, 2.0)]
                },
                {
                    'name': 'Late obstacle (after passage)',
                    'obstacles': [(10.0, 10.0, 4.0, 4.0, 20.0, 5.0)]
                },
                {
                    'name': 'Concurrent obstacle',
                    'obstacles': [(10.0, 10.0, 4.0, 4.0, 5.0, 10.0)]
                },
                {
                    'name': 'Multiple obstacles',
                    'obstacles': [
                        (10.0, 10.0, 3.0, 3.0, 2.0, 4.0),
                        (20.0, 20.0, 3.0, 3.0, 8.0, 6.0)
                    ]
                }
            ]
            
            print("Testing different obstacle scenarios:")
            
            for scenario in scenarios:
                path_time = data_generator._astar_pathfinding(
                    start_pos, end_pos, scenario['obstacles'], start_time
                )
                
                print(f"  {scenario['name']}: {path_time:.4f}s")
            
            print("✓ All obstacle scenarios tested")
            return True
        else:
            print("⚠ Not enough task points for scenario testing")
            return True
        
    except Exception as e:
        print(f"✗ Obstacle scenarios test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_performance():
    """测试性能"""
    print("\n=== Testing Performance ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        data_generator = predictor.data_generator
        
        task_points = data_generator.task_points
        
        if len(task_points) >= 2:
            point_ids = list(task_points.keys())
            start_pos = task_points[point_ids[0]]
            end_pos = task_points[point_ids[-1]]
            
            obstacles = [
                (15.0, 15.0, 4.0, 4.0, 2.0, 8.0),
                (25.0, 25.0, 3.0, 3.0, 5.0, 6.0)
            ]
            start_time = 0.0
            
            # 测试多次执行
            num_tests = 20
            total_time = 0
            successful_runs = 0
            
            for _ in range(num_tests):
                start_exec_time = time.time()
                path_time = data_generator._astar_pathfinding(
                    start_pos, end_pos, obstacles, start_time
                )
                exec_time = time.time() - start_exec_time
                
                if path_time is not None:
                    total_time += exec_time
                    successful_runs += 1
            
            if successful_runs > 0:
                avg_time = total_time / successful_runs
                print(f"Performance results ({successful_runs}/{num_tests} successful):")
                print(f"  Average execution time: {avg_time*1000:.2f}ms")
                
                if avg_time < 0.01:  # 小于10ms
                    print("✓ Excellent performance")
                elif avg_time < 0.05:  # 小于50ms
                    print("✓ Good performance")
                else:
                    print("⚠ Performance might be slow")
                
                return True
            else:
                print("✗ All performance tests failed")
                return False
        else:
            print("⚠ Not enough task points for performance testing")
            return True
        
    except Exception as e:
        print(f"✗ Performance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_integration():
    """测试模型集成"""
    print("\n=== Testing Model Integration ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 生成样本
        sample = predictor.data_generator.generate_sample()
        
        print(f"Model integration test:")
        print(f"  Sample actual time: {sample['actual_time']:.4f}s")
        print(f"  Sample distance: {sample['distance']:.4f}m")
        print(f"  AGV speed: {sample['agv_speed']:.4f}m/s")
        
        # 测试模型前向传播
        batch_data = [sample]
        
        try:
            predictions = predictor.model(batch_data)
            print(f"✓ Model forward pass successful")
            print(f"  Prediction: {predictions[0].item():.4f}s")
            print(f"  Actual time: {sample['actual_time']:.4f}s")
            print(f"  Error: {abs(predictions[0].item() - sample['actual_time']):.4f}s")
            return True
        except Exception as e:
            print(f"✗ Model forward pass failed: {e}")
            return False
        
    except Exception as e:
        print(f"✗ Model integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("Delay-based A* Algorithm Test")
    print("=" * 40)
    
    tests = [
        ("Delay Calculation", test_delay_calculation),
        ("A* with Delays", test_astar_with_delays),
        ("Actual Time Calculation", test_actual_time_calculation),
        ("Different Obstacle Scenarios", test_different_obstacle_scenarios),
        ("Performance", test_performance),
        ("Model Integration", test_model_integration),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ Test '{test_name}' crashed: {e}")
            results[test_name] = False
    
    # 总结结果
    print(f"\n{'='*40}")
    print("Test Results Summary:")
    print(f"{'='*40}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<30} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed!")
        print("\nDelay-based A* Features:")
        print("✓ Dynamic edge weight adjustment")
        print("✓ Obstacle duration-based delays")
        print("✓ Time-aware pathfinding")
        print("✓ No edge blocking (all paths available)")
        print("✓ Realistic delay modeling")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
