#!/usr/bin/env python3
"""
Test script for obstacle features without coverage information
测试去掉覆盖度信息后的障碍物特征
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from transport_time_predictor import TransportTimePredictorInterface
import time


def test_no_coverage_features():
    """测试去掉覆盖度信息的特征"""
    print("=== Testing Features Without Coverage Information ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 生成样本
        sample = predictor.data_generator.generate_sample()
        graph_data = sample['graph_data']
        
        print(f"Generated sample with simplified obstacle features:")
        print(f"  Node features shape: {graph_data.x.shape}")
        print(f"  Edge features shape: {graph_data.edge_attr.shape}")
        print(f"  Expected node features: 23 (3 basic + 10*2 obstacle time)")
        print(f"  Expected edge features: 22 (2 basic + 10*2 obstacle time)")
        
        # 验证特征维度
        expected_node_features = 23  # [x, y, has_obstacle] + 10 * [start, end]
        expected_edge_features = 22  # [distance, has_obstacle] + 10 * [start, end]
        
        actual_node_features = graph_data.x.shape[1]
        actual_edge_features = graph_data.edge_attr.shape[1]
        
        if actual_node_features == expected_node_features:
            print(f"✓ Node features dimension correct: {actual_node_features}")
        else:
            print(f"✗ Node features dimension mismatch: expected {expected_node_features}, got {actual_node_features}")
            return False
        
        if actual_edge_features == expected_edge_features:
            print(f"✓ Edge features dimension correct: {actual_edge_features}")
        else:
            print(f"✗ Edge features dimension mismatch: expected {expected_edge_features}, got {actual_edge_features}")
            return False
        
        # 分析特征内容
        print(f"\nFeature content analysis:")
        
        # 检查节点特征
        nodes_with_obstacles = 0
        for i in range(graph_data.x.shape[0]):
            node_features = graph_data.x[i]
            x, y, has_obstacle = node_features[:3]
            if has_obstacle > 0:
                nodes_with_obstacles += 1
                print(f"  Node {i}: position=({x:.2f}, {y:.2f}), has_obstacle={has_obstacle:.0f}")
                
                # 检查障碍物时间信息
                for j in range(10):
                    start_idx = 3 + j * 2
                    obs_start, obs_end = node_features[start_idx:start_idx+2]
                    if obs_start > 0 or obs_end > 0:
                        print(f"    Obstacle {j+1}: start={obs_start:.3f}, end={obs_end:.3f}")
        
        # 检查边特征
        edges_with_obstacles = 0
        for i in range(graph_data.edge_attr.shape[0]):
            edge_features = graph_data.edge_attr[i]
            distance, has_obstacle = edge_features[:2]
            if has_obstacle > 0:
                edges_with_obstacles += 1
        
        print(f"  Nodes with obstacles: {nodes_with_obstacles}")
        print(f"  Edges with obstacles: {edges_with_obstacles}")
        
        return True
        
    except Exception as e:
        print(f"✗ No coverage features test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_simplified_time_calculation():
    """测试简化的时间计算"""
    print("\n=== Testing Simplified Time Calculation ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 生成多个样本并分析时间计算
        num_samples = 30
        base_times = []
        actual_times = []
        delays = []
        
        for _ in range(num_samples):
            sample = predictor.data_generator.generate_sample()
            
            distance = sample['distance']
            agv_speed = sample['agv_speed']
            base_time = distance / agv_speed
            actual_time = sample['actual_time']
            delay = actual_time - base_time
            
            base_times.append(base_time)
            actual_times.append(actual_time)
            delays.append(delay)
        
        print(f"Time calculation analysis ({num_samples} samples):")
        print(f"  Average base time: {np.mean(base_times):.4f}s")
        print(f"  Average actual time: {np.mean(actual_times):.4f}s")
        print(f"  Average delay: {np.mean(delays):.4f}s")
        print(f"  Max delay: {np.max(delays):.4f}s")
        print(f"  Samples with delay > 0.01s: {sum(1 for d in delays if d > 0.01)}/{num_samples}")
        
        # 验证时间计算的合理性
        valid_times = all(actual >= base - 0.001 for actual, base in zip(actual_times, base_times))
        if valid_times:
            print("✓ All actual times >= base times (correct)")
        else:
            print("⚠ Some actual times < base times (potential issue)")
        
        return True
        
    except Exception as e:
        print(f"✗ Simplified time calculation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_compatibility():
    """测试模型兼容性"""
    print("\n=== Testing Model Compatibility ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 生成样本
        sample = predictor.data_generator.generate_sample()
        
        print(f"Model compatibility test:")
        print(f"  Graph data shape: nodes={sample['graph_data'].x.shape}, edges={sample['graph_data'].edge_attr.shape}")
        
        # 测试模型前向传播
        batch_data = [sample]
        
        try:
            predictions = predictor.model(batch_data)
            print(f"✓ Model forward pass successful")
            print(f"  Prediction: {predictions[0].item():.4f}s")
            print(f"  Actual time: {sample['actual_time']:.4f}s")
            return True
        except Exception as e:
            print(f"✗ Model forward pass failed: {e}")
            return False
        
    except Exception as e:
        print(f"✗ Model compatibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_training_performance():
    """测试训练性能"""
    print("\n=== Testing Training Performance ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 使用小规模配置
        predictor.config.update({
            'num_epochs': 3,
            'batch_size': 8,
            'graph_hidden_dim': 32,
            'temporal_hidden_dim': 64
        })
        
        # 重新创建模型
        from transport_time_predictor import TransportTimePredictor
        predictor.model = TransportTimePredictor(predictor.config)
        predictor.model.to(predictor.device)
        
        print("Starting training with simplified features...")
        start_time = time.time()
        
        # 训练模型
        predictor.train_model(
            num_train_samples=80,
            num_val_samples=20
        )
        
        training_time = time.time() - start_time
        print(f"✓ Training completed in {training_time:.2f}s")
        
        # 测试预测
        pred_time = predictor.predict_transport_time('P1', 'P10', 1.0, 0.0)
        print(f"Post-training prediction: {pred_time:.4f}s")
        
        return True
        
    except Exception as e:
        print(f"✗ Training performance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_performance_metrics():
    """测试性能指标"""
    print("\n=== Testing Performance Metrics ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 测试样本生成性能
        num_samples = 50
        start_time = time.time()
        
        for _ in range(num_samples):
            sample = predictor.data_generator.generate_sample()
        
        generation_time = time.time() - start_time
        
        print(f"Performance metrics:")
        print(f"  Sample generation: {generation_time/num_samples*1000:.2f}ms per sample")
        
        # 测试预测性能
        start_time = time.time()
        
        for _ in range(num_samples):
            pred_time = predictor.predict_transport_time('P1', 'P10', 1.0, 0.0)
        
        prediction_time = time.time() - start_time
        
        print(f"  Prediction time: {prediction_time/num_samples*1000:.2f}ms per prediction")
        
        # 性能基准
        if generation_time/num_samples < 0.01:  # < 10ms per sample
            print("✓ Sample generation performance is good")
        else:
            print("⚠ Sample generation might be slow")
        
        if prediction_time/num_samples < 0.005:  # < 5ms per prediction
            print("✓ Prediction performance is good")
        else:
            print("⚠ Prediction might be slow")
        
        return True
        
    except Exception as e:
        print(f"✗ Performance metrics test failed: {e}")
        return False


def visualize_simplified_features():
    """可视化简化特征"""
    print("\n=== Visualizing Simplified Features ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 生成样本
        num_samples = 100
        samples = []
        for _ in range(num_samples):
            sample = predictor.data_generator.generate_sample()
            samples.append(sample)
        
        # 提取数据
        base_times = []
        actual_times = []
        delays = []
        distances = []
        
        for sample in samples:
            distance = sample['distance']
            speed = sample['agv_speed']
            actual_time = sample['actual_time']
            base_time = distance / speed
            delay = actual_time - base_time
            
            base_times.append(base_time)
            actual_times.append(actual_time)
            delays.append(delay)
            distances.append(distance)
        
        # 创建可视化
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 1. 基础时间 vs 实际时间
        axes[0, 0].scatter(base_times, actual_times, alpha=0.6, s=30)
        min_time, max_time = min(base_times + actual_times), max(base_times + actual_times)
        axes[0, 0].plot([min_time, max_time], [min_time, max_time], 'r--', label='y=x')
        axes[0, 0].set_xlabel('Base Time (s)')
        axes[0, 0].set_ylabel('Actual Time (s)')
        axes[0, 0].set_title('Base Time vs Actual Time (No Coverage)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 延迟分布
        axes[0, 1].hist(delays, bins=20, alpha=0.7, color='orange')
        axes[0, 1].set_xlabel('Delay (s)')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].set_title('Delay Distribution (Simplified Model)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 距离 vs 延迟
        axes[1, 0].scatter(distances, delays, alpha=0.6, s=30)
        axes[1, 0].set_xlabel('Distance (m)')
        axes[1, 0].set_ylabel('Delay (s)')
        axes[1, 0].set_title('Distance vs Delay')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 特征维度对比
        feature_types = ['Node Features', 'Edge Features']
        old_dims = [43, 42]  # 之前的维度
        new_dims = [23, 22]  # 现在的维度
        
        x = np.arange(len(feature_types))
        width = 0.35
        
        axes[1, 1].bar(x - width/2, old_dims, width, label='With Coverage', alpha=0.7)
        axes[1, 1].bar(x + width/2, new_dims, width, label='Without Coverage', alpha=0.7)
        axes[1, 1].set_xlabel('Feature Type')
        axes[1, 1].set_ylabel('Dimension')
        axes[1, 1].set_title('Feature Dimension Comparison')
        axes[1, 1].set_xticks(x)
        axes[1, 1].set_xticklabels(feature_types)
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('no_coverage_features.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print("✓ Simplified features visualization saved as 'no_coverage_features.png'")
        return True
        
    except Exception as e:
        print(f"✗ Simplified features visualization failed: {e}")
        return False


def main():
    """主测试函数"""
    print("No Coverage Information Test")
    print("=" * 40)
    
    tests = [
        ("No Coverage Features", test_no_coverage_features),
        ("Simplified Time Calculation", test_simplified_time_calculation),
        ("Model Compatibility", test_model_compatibility),
        ("Training Performance", test_training_performance),
        ("Performance Metrics", test_performance_metrics),
        ("Simplified Features Visualization", visualize_simplified_features),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ Test '{test_name}' crashed: {e}")
            results[test_name] = False
    
    # 总结结果
    print(f"\n{'='*40}")
    print("Test Results Summary:")
    print(f"{'='*40}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<35} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed!")
        print("\nKey simplifications:")
        print("✓ Removed all coverage calculations")
        print("✓ Node features: 43 → 23 dimensions")
        print("✓ Edge features: 42 → 22 dimensions")
        print("✓ Simplified delay model")
        print("✓ Maintained intersection detection")
        print("✓ Improved computational efficiency")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
