# Visualization Interface User Guide

## Overview

The new visualization interface uses a left-right split layout, providing comprehensive system status monitoring.

## Interface Layout

### Left Side: Map Area (60% width)
- **Task Points**: Black dots with point ID labels
- **Channels**: Gray lines connecting task points
- **AGVs**: Colored dots showing AGV position and status
  - Green: Idle AGV
  - Blue: Busy AGV
  - Orange: Charging AGV
  - Red: Error AGV
- **Tasks**: Arrows showing task start to end points
  - Gray: Waiting tasks
  - Blue: Assigned tasks
  - Orange: In progress tasks
  - Green: Completed tasks
- **Obstacles**: Red semi-transparent rectangles
- **AGV Paths**: Dashed lines showing AGV planned paths

### Right Side: Information Panel (40% width - Two Columns)

The information panel is divided into two columns to prevent information overlap:

#### Left Column: SYSTEM & TASKS
- System Statistics
- Task Pool Status

#### Right Column: AGV & PATHS
- AGV Status Information
- Path Planning Information

## Information Panel Layout

### Left Column: SYSTEM & TASKS

#### 1. System Statistics
```
=== SYSTEM STATS ===
Runtime: 30.0s
AGVs: 5
Tasks: 15
Obstacles: 2
Completed: 8
Utilization: 80.0%
```

#### 2. Task Pool Status
```
=== TASK POOL ===
Total: 15
Waiting:   8
Assigned:   4
In Progress:   2
Completed:   1

--- TASK TYPES ---
transport:   10
pickup:   3
delivery:   2
```

### Right Column: AGV & PATHS

#### 3. AGV Status
```
=== AGV STATUS ===
Idle:   1
Busy:   4
Charging:   0
Error:   0

--- ASSIGNMENTS ---
agv_0: P1->P5
agv_1: P3->P8
agv_2: P6->P12
agv_3: P9->P15
```

#### 4. Path Planning
```
=== PATH PLANNING ===
With Path:   4
No Path:   1

--- PROGRESS ---
agv_0: 2/5 (40%)
agv_1: 1/3 (33%)
agv_2: 4/6 (67%)
agv_3: 0/4 (0%)
```

## Visualization Modes

### 1. real-time
- Real-time display updates
- Suitable for observing system operation
- Shows dynamic changes

### 2. step
- Step-by-step updates for detailed observation
- Suitable for debugging and analysis
- Pauses at each step

### 3. fast
- Fast execution without pauses
- Suitable for quick result verification
- High speed multiplier

### 4. final
- Shows only final state
- Suitable for viewing final results

### 5. none
- Text-only output
- Suitable for batch runs

## Usage Examples

### Basic Usage
```bash
# Real-time visualization
python run_simulation.py --visualization real-time

# Fast mode
python run_simulation.py --config-template fast --visualization fast

# Step-by-step debugging
python run_simulation.py --visualization step --speed 1.0
```

### Configuration Parameters
```bash
# Specify case and time
python run_simulation.py --case 2 --max-time 300 --visualization real-time

# Adjust visualization speed
python run_simulation.py --visualization real-time --speed 20.0
```

## Information Panel Details

### System Statistics
- **Runtime**: Current simulation time
- **AGVs**: Total number of AGVs in system
- **Tasks**: Number of tasks in task pool
- **Obstacles**: Number of currently active obstacles
- **Completed**: Cumulative number of completed tasks
- **Utilization**: Percentage of busy AGVs

### Task Pool Status
- **Total**: Total number of tasks in pool
- **Waiting**: Number of tasks waiting for assignment
- **Assigned**: Number of assigned but not started tasks
- **In Progress**: Number of tasks currently being executed
- **Completed**: Number of completed tasks
- **Task Types**: Task distribution by type

### AGV Status
- **Idle**: Number of AGVs without tasks
- **Busy**: Number of AGVs executing tasks
- **Charging**: Number of AGVs charging
- **Error**: Number of AGVs with errors
- **Assignments**: Shows current task path for each AGV

### Path Planning
- **With Path**: Number of AGVs with planned paths
- **No Path**: Number of AGVs without planned paths
- **Progress**: Shows path execution progress for each AGV

## Notes

1. **Font Warnings**: Font warnings don't affect functionality and can be ignored
2. **Performance**: Real-time mode may affect performance in large-scale simulations
3. **Display Limits**: Information panel limits number of displayed items to avoid overload
4. **Color Coding**: Different colors represent different states, refer to legend

## Troubleshooting

### Visualization Not Showing
- Check if matplotlib is installed
- Confirm visualization mode is not 'none'
- Check system GUI support

### Font Display Issues
- Font warnings don't affect functionality
- Font-related warnings can be ignored

### Performance Issues
- Use fast mode to improve execution speed
- Reduce visualization update frequency
- Consider using none mode for batch testing
