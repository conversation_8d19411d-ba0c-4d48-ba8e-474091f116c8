# 可视化界面使用指南

## 概述

新的可视化界面采用左右分栏布局，提供了全面的系统状态监控功能。

## 界面布局

### 左侧：地图区域（占2/3宽度）
- **任务点**：黑色圆点，标注点位ID
- **通道**：灰色线条连接任务点
- **AGV**：彩色圆点，显示AGV位置和状态
  - 绿色：空闲AGV
  - 蓝色：忙碌AGV
  - 橙色：充电AGV
  - 红色：错误AGV
- **任务**：箭头显示任务起点到终点
  - 灰色：等待任务
  - 蓝色：已分配任务
  - 橙色：进行中任务
  - 绿色：已完成任务
- **障碍物**：红色半透明矩形
- **AGV路径**：虚线显示AGV规划路径

### 右侧：信息面板（占1/3宽度）

#### 1. 系统统计
```
=== 系统统计 ===
运行时间: 30.0s
AGV数量: 5
当前任务: 15
活跃障碍物: 2
已完成任务: 8
AGV利用率: 80.0%
```

#### 2. 任务池状态
```
=== 任务池状态 ===
总数: 15
等待:   8
已分配:   4
进行中:   2
已完成:   1

--- 任务类型 ---
transport:   10
pickup:   3
delivery:   2
```

#### 3. AGV状态
```
=== AGV状态 ===
空闲:   1
忙碌:   4
充电:   0
错误:   0

--- 任务分配 ---
agv_0: P1->P5
agv_1: P3->P8
agv_2: P6->P12
agv_3: P9->P15
```

#### 4. 路径规划
```
=== 路径规划 ===
有路径:   4
无路径:   1

--- 路径进度 ---
agv_0: 2/5 (40%)
agv_1: 1/3 (33%)
agv_2: 4/6 (67%)
agv_3: 0/4 (0%)
```

## 可视化模式

### 1. real-time（实时模式）
- 实时更新显示
- 适合观察系统运行过程
- 可以看到动态变化

### 2. step（逐步模式）
- 逐步更新，便于详细观察
- 适合调试和分析
- 每步都有暂停

### 3. fast（快速模式）
- 快速运行，无暂停
- 适合快速验证结果
- 高速度倍数

### 4. final（最终结果）
- 仅显示最终状态
- 适合查看最终结果

### 5. none（无可视化）
- 纯文本输出
- 适合批量运行

## 使用示例

### 基本使用
```bash
# 实时可视化
python run_simulation.py --visualization real-time

# 快速模式
python run_simulation.py --config-template fast --visualization fast

# 逐步调试
python run_simulation.py --visualization step --speed 1.0
```

### 配置参数
```bash
# 指定case和时间
python run_simulation.py --case 2 --max-time 300 --visualization real-time

# 调整可视化速度
python run_simulation.py --visualization real-time --speed 20.0
```

## 信息面板详解

### 系统统计
- **运行时间**：当前仿真时间
- **AGV数量**：系统中AGV总数
- **当前任务**：任务池中的任务数量
- **活跃障碍物**：当前生效的障碍物数量
- **已完成任务**：累计完成的任务数
- **AGV利用率**：忙碌AGV占总AGV的百分比

### 任务池状态
- **总数**：任务池中任务总数
- **等待**：等待分配的任务数
- **已分配**：已分配但未开始的任务数
- **进行中**：正在执行的任务数
- **已完成**：已完成的任务数
- **任务类型**：按类型统计的任务分布

### AGV状态
- **空闲**：无任务的AGV数量
- **忙碌**：正在执行任务的AGV数量
- **充电**：正在充电的AGV数量
- **错误**：出现错误的AGV数量
- **任务分配**：显示每个AGV当前的任务路径

### 路径规划
- **有路径**：已规划路径的AGV数量
- **无路径**：未规划路径的AGV数量
- **路径进度**：显示每个AGV的路径执行进度

## 注意事项

1. **字体警告**：中文字体警告不影响功能，可以忽略
2. **性能**：实时模式在大规模仿真时可能影响性能
3. **显示限制**：信息面板限制显示条目数量，避免信息过载
4. **颜色编码**：不同颜色代表不同状态，参考图例

## 故障排除

### 可视化不显示
- 检查matplotlib是否安装
- 确认可视化模式不是'none'
- 检查系统图形界面支持

### 中文显示问题
- 中文字体警告不影响功能
- 可以忽略字体相关警告信息

### 性能问题
- 使用fast模式提高运行速度
- 减少可视化更新频率
- 考虑使用none模式进行批量测试
