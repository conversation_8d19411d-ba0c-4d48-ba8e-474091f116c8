#!/usr/bin/env python3
"""
Demo script for Transport Time Predictor
运输时间预测器演示脚本
"""

import numpy as np
import matplotlib.pyplot as plt
from transport_time_predictor import TransportTimePredictorInterface
from transport_predictor_config import get_config
import time


def demo_basic_prediction():
    """演示基本预测功能"""
    print("=== 基本预测功能演示 ===")
    
    # 创建预测器
    predictor = TransportTimePredictorInterface()
    
    # 获取可用的点ID
    available_points = list(predictor.graph_structure['task_points'].keys())
    start_point = available_points[0]  # 第一个点
    end_point = available_points[-1]   # 最后一个点
    mid_point = available_points[len(available_points)//2]  # 中间点

    print(f"使用的测试点: {start_point} -> {end_point}")

    # 测试不同场景的预测
    scenarios = [
        {
            'name': '无障碍物场景',
            'start': start_point,
            'end': end_point,
            'speed': 1.0,
            'start_time': 0.0,
            'obstacles': []
        },
        {
            'name': '有障碍物场景',
            'start': start_point,
            'end': end_point,
            'speed': 1.0,
            'start_time': 0.0,
            'obstacles': [(mid_point, 10.0, 25.0)]
        },
        {
            'name': '高速AGV场景',
            'start': start_point,
            'end': end_point,
            'speed': 2.0,
            'start_time': 0.0,
            'obstacles': []
        },
        {
            'name': '低速AGV场景',
            'start': start_point,
            'end': end_point,
            'speed': 0.5,
            'start_time': 0.0,
            'obstacles': []
        }
    ]
    
    for scenario in scenarios:
        pred_time = predictor.predict_transport_time(
            start_point=scenario['start'],
            end_point=scenario['end'],
            agv_speed=scenario['speed'],
            start_time=scenario['start_time'],
            obstacles=scenario['obstacles']
        )
        print(f"{scenario['name']}: {pred_time:.2f}秒")
    
    print()


def demo_speed_analysis():
    """演示速度对运输时间的影响"""
    print("=== 速度影响分析 ===")
    
    predictor = TransportTimePredictorInterface()

    # 获取可用的点ID
    available_points = list(predictor.graph_structure['task_points'].keys())
    start_point = available_points[0]
    end_point = available_points[-1]
    start_idx = predictor.data_generator.point_to_idx[start_point]
    end_idx = predictor.data_generator.point_to_idx[end_point]

    speeds = np.arange(0.5, 3.0, 0.1)
    predictions = []
    actual_times = []

    for speed in speeds:
        # 预测时间
        pred_time = predictor.predict_transport_time(
            start_point=start_point,
            end_point=end_point,
            agv_speed=speed,
            start_time=0.0,
            obstacles=[]
        )
        predictions.append(pred_time)

        # 计算理论时间（基于距离）
        distance = predictor.data_generator.distance_matrix[start_idx, end_idx]
        if np.isinf(distance):
            distance = 8.0  # 估算距离
        actual_time = distance / speed
        actual_times.append(actual_time)
    
    # 绘制结果
    plt.figure(figsize=(10, 6))
    plt.plot(speeds, predictions, 'b-', label='Predicted Time', linewidth=2)
    plt.plot(speeds, actual_times, 'r--', label='Theoretical Time', linewidth=2)
    plt.xlabel('AGV Speed (m/s)')
    plt.ylabel('Transport Time (s)')
    plt.title('Impact of AGV Speed on Transport Time')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('speed_analysis.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"速度范围: {speeds[0]:.1f} - {speeds[-1]:.1f} m/s")
    print(f"预测时间范围: {min(predictions):.2f} - {max(predictions):.2f} s")
    print(f"理论时间范围: {min(actual_times):.2f} - {max(actual_times):.2f} s")
    print()


def demo_obstacle_impact():
    """演示障碍物对运输时间的影响"""
    print("=== 障碍物影响分析 ===")
    
    predictor = TransportTimePredictorInterface()

    # 获取可用的点ID
    available_points = list(predictor.graph_structure['task_points'].keys())
    start_point = available_points[0]
    end_point = available_points[-1]
    mid_point = available_points[len(available_points)//2]

    # 基准场景（无障碍物）
    baseline = predictor.predict_transport_time(
        start_point=start_point,
        end_point=end_point,
        agv_speed=1.0,
        start_time=0.0,
        obstacles=[]
    )

    # 不同障碍物配置
    obstacle_configs = [
        {
            'name': '起点障碍物',
            'obstacles': [(start_point, 5.0, 15.0)]
        },
        {
            'name': '终点障碍物',
            'obstacles': [(end_point, 10.0, 20.0)]
        },
        {
            'name': '路径中间障碍物',
            'obstacles': [(mid_point, 8.0, 18.0)]
        },
        {
            'name': '多个障碍物',
            'obstacles': [(start_point, 5.0, 10.0), (mid_point, 12.0, 18.0), (end_point, 20.0, 25.0)]
        }
    ]

    print(f"基准时间（无障碍物）: {baseline:.2f}秒")

    for config in obstacle_configs:
        pred_time = predictor.predict_transport_time(
            start_point=start_point,
            end_point=end_point,
            agv_speed=1.0,
            start_time=0.0,
            obstacles=config['obstacles']
        )
        delay = pred_time - baseline
        print(f"{config['name']}: {pred_time:.2f}秒 (延迟: {delay:.2f}秒)")
    
    print()


def demo_data_generation():
    """演示数据生成功能"""
    print("=== 数据生成演示 ===")
    
    predictor = TransportTimePredictorInterface()
    
    # 生成样本数据
    print("生成训练样本...")
    start_time = time.time()
    samples = predictor.data_generator.generate_dataset(1000)
    generation_time = time.time() - start_time
    
    print(f"生成了 {len(samples)} 个样本，耗时 {generation_time:.2f}秒")
    
    # 分析样本统计
    actual_times = [sample['actual_time'] for sample in samples]
    agv_speeds = [sample['agv_speed'] for sample in samples]
    distances = [sample['distance'] for sample in samples]
    
    print(f"\n样本统计:")
    print(f"实际运输时间: {np.mean(actual_times):.2f} ± {np.std(actual_times):.2f}秒")
    print(f"AGV速度: {np.mean(agv_speeds):.2f} ± {np.std(agv_speeds):.2f} m/s")
    print(f"运输距离: {np.mean(distances):.2f} ± {np.std(distances):.2f} m")
    
    # 绘制分布图
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    axes[0].hist(actual_times, bins=30, alpha=0.7, color='blue')
    axes[0].set_xlabel('Actual Transport Time (s)')
    axes[0].set_ylabel('Frequency')
    axes[0].set_title('Transport Time Distribution')
    axes[0].grid(True, alpha=0.3)

    axes[1].hist(agv_speeds, bins=30, alpha=0.7, color='green')
    axes[1].set_xlabel('AGV Speed (m/s)')
    axes[1].set_ylabel('Frequency')
    axes[1].set_title('AGV Speed Distribution')
    axes[1].grid(True, alpha=0.3)

    axes[2].hist(distances, bins=30, alpha=0.7, color='red')
    axes[2].set_xlabel('Transport Distance (m)')
    axes[2].set_ylabel('Frequency')
    axes[2].set_title('Transport Distance Distribution')
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('data_distribution.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print()


def demo_graph_structure():
    """演示图结构"""
    print("=== 图结构演示 ===")
    
    predictor = TransportTimePredictorInterface()
    
    task_points = predictor.graph_structure['task_points']
    channels = predictor.graph_structure['channels']

    print(f"任务点数量: {len(task_points)}")
    print(f"通道数量: {len(channels)}")

    # 显示部分任务点
    print("\n部分任务点坐标:")
    for i, (point_id, coords) in enumerate(list(task_points.items())[:10]):
        print(f"  {point_id}: ({coords['x']}, {coords['y']})")
    
    # 显示连接性统计
    adjacency_matrix = predictor.data_generator.adjacency_matrix
    connectivity = np.sum(adjacency_matrix, axis=1)
    
    print(f"\n连接性统计:")
    print(f"平均连接数: {np.mean(connectivity):.2f}")
    print(f"最大连接数: {int(np.max(connectivity))}")
    print(f"最小连接数: {int(np.min(connectivity))}")
    
    # 绘制图结构
    plt.figure(figsize=(12, 10))
    
    # 绘制任务点
    x_coords = [point['x'] for point in task_points.values()]
    y_coords = [point['y'] for point in task_points.values()]
    plt.scatter(x_coords, y_coords, c='blue', s=100, alpha=0.7, label='Task Points')

    # 添加点标签
    for point_id, coords in task_points.items():
        plt.annotate(point_id, (coords['x'], coords['y']),
                    xytext=(5, 5), textcoords='offset points', fontsize=8)

    # 绘制通道
    for (start, end), _ in channels.items():
        if start in task_points and end in task_points:
            start_coords = task_points[start]
            end_coords = task_points[end]
            plt.plot([start_coords['x'], end_coords['x']],
                    [start_coords['y'], end_coords['y']],
                    'gray', alpha=0.5, linewidth=1)

    plt.xlabel('X Coordinate')
    plt.ylabel('Y Coordinate')
    plt.title('AGV System Graph Structure')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.axis('equal')
    plt.savefig('graph_structure.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print()


def main():
    """主演示函数"""
    print("Transport Time Predictor - GNN + LSTM 演示")
    print("=" * 50)
    print()
    
    try:
        # 基本预测演示
        demo_basic_prediction()
        
        # 速度影响分析
        demo_speed_analysis()
        
        # 障碍物影响分析
        demo_obstacle_impact()
        
        # 数据生成演示
        demo_data_generation()
        
        # 图结构演示
        demo_graph_structure()
        
        print("=" * 50)
        print("演示完成！")
        print()
        print("生成的文件:")
        print("- speed_analysis.png: 速度影响分析图")
        print("- data_distribution.png: 数据分布图")
        print("- graph_structure.png: 图结构可视化")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
