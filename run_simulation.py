"""
运行仿真实验的脚本
"""
import os
import argparse

# 尝试导入仿真模块
try:
    from simulation import Simulation, VISUALIZATION_AVAILABLE
except ImportError as e:
    print(f"错误: 无法导入仿真模块: {e}")
    exit(1)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='运行AGV系统仿真实验')

    parser.add_argument('--case', type=int, default=1, help='实验case编号')
    parser.add_argument('--time-step', type=float, default=0.1, help='仿真时间步长（秒）')
    parser.add_argument('--max-time', type=float, default=3600, help='最大仿真时间（秒）')
    parser.add_argument('--task-file', type=str, default='output/tasks/tasks_case1_test.json', help='任务文件路径')
    parser.add_argument('--obstacle-file', type=str, default='output/obstacles/obstacles_case1_test.json', help='障碍物文件路径')
    parser.add_argument('--agv-file', type=str, default='output/tasks/tasks_case1_agvs_test.json', help='AGV文件路径')
    parser.add_argument('--output-dir', type=str, default='output', help='输出目录')
    parser.add_argument('--visualization', type=str, default='real-time',
                        choices=['real-time', 'step', 'final', 'none'],
                        help='可视化模式')
    parser.add_argument('--speed', type=float, default=10.0, help='可视化速度倍数（1.0为实时，>1.0为加速）')

    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 检查可视化模式
    if not VISUALIZATION_AVAILABLE and args.visualization != 'none':
        print(f"警告: matplotlib库未安装，将使用文本模式运行仿真")
        visualization_mode = 'none'
    else:
        visualization_mode = args.visualization

    # 创建仿真实例
    simulation = Simulation(
        case_number=args.case,
        simulation_time_step=args.time_step,
        max_simulation_time=args.max_time,
        visualization_speed=args.speed
    )

    # 设置可视化模式
    simulation.visualizer.mode = visualization_mode

    # 加载任务、障碍物和AGV
    simulation.load_tasks(args.task_file)
    simulation.load_obstacles(args.obstacle_file)
    simulation.load_agvs(args.agv_file)

    # 运行仿真
    performance = simulation.run()

    # 打印性能指标
    print("\n性能指标:")
    for metric, value in performance.items():
        print(f"{metric}: {value}")

if __name__ == "__main__":
    main()
