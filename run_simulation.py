"""
运行仿真实验的脚本
"""
import os
import argparse
from simulation_config import SimulationConfig, get_config, create_custom_config

# 尝试导入仿真模块
try:
    from simulation import Simulation, VISUALIZATION_AVAILABLE
except ImportError as e:
    print(f"错误: 无法导入仿真模块: {e}")
    exit(1)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='运行AGV系统仿真实验')

    # 配置相关参数
    parser.add_argument('--config-template', type=str, default='default',
                        choices=['default', 'fast', 'debug', 'production'],
                        help='配置模板: default(默认), fast(快速), debug(调试), production(生产)')
    parser.add_argument('--config-file', type=str, default=None, help='自定义配置文件路径')
    parser.add_argument('--save-config', type=str, default=None, help='保存当前配置到文件')

    # 基本仿真参数
    parser.add_argument('--case', type=int, default=None, help='实验case编号')
    parser.add_argument('--time-step', type=float, default=None, help='仿真时间步长（秒）')
    parser.add_argument('--max-time', type=float, default=None, help='最大仿真时间（秒）')

    # 文件路径参数
    parser.add_argument('--task-file', type=str, default=None, help='任务文件路径')
    parser.add_argument('--obstacle-file', type=str, default=None, help='障碍物文件路径')
    parser.add_argument('--agv-file', type=str, default=None, help='AGV文件路径')
    parser.add_argument('--output-dir', type=str, default=None, help='输出目录')

    # 可视化参数
    parser.add_argument('--visualization', type=str, default=None,
                        choices=['real-time', 'step', 'fast', 'final', 'none'],
                        help='可视化模式: real-time(实时), step(逐步), fast(快速无暂停), final(仅最终结果), none(无可视化)')
    parser.add_argument('--speed', type=float, default=None, help='可视化速度倍数（1.0为实时，>1.0为加速）')

    # 算法参数
    parser.add_argument('--task-algorithm', type=str, default=None,
                        choices=['greedy', 'hungarian', 'auction', 'custom'],
                        help='任务分配算法')
    parser.add_argument('--path-algorithm', type=str, default=None,
                        choices=['astar', 'dijkstra', 'rrt', 'custom'],
                        help='路径规划算法')
    parser.add_argument('--reschedule-algorithm', type=str, default=None,
                        choices=['replan', 'reassign', 'hybrid', 'custom'],
                        help='任务重调度算法')

    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 加载配置
    if args.config_file:
        # 从文件加载配置
        config = SimulationConfig()
        if config.load_from_file(args.config_file):
            print(f"成功加载配置文件: {args.config_file}")
        else:
            print(f"加载配置文件失败，使用默认配置")
            config = get_config(args.config_template)
    else:
        # 使用配置模板
        config = get_config(args.config_template)
        print(f"使用配置模板: {args.config_template}")

    # 用命令行参数覆盖配置
    if args.case is not None:
        config.update_case_number(args.case)
    if args.time_step is not None:
        config.SIMULATION_TIME_STEP = args.time_step
    if args.max_time is not None:
        config.MAX_SIMULATION_TIME = args.max_time
    if args.visualization is not None:
        config.VISUALIZATION_MODE = args.visualization
    if args.speed is not None:
        config.VISUALIZATION_SPEED = args.speed
    if args.task_file is not None:
        config.TASK_FILE_PATH = args.task_file
    if args.obstacle_file is not None:
        config.OBSTACLE_FILE_PATH = args.obstacle_file
    if args.agv_file is not None:
        config.AGV_FILE_PATH = args.agv_file
    if args.output_dir is not None:
        config.OUTPUT_DIR = args.output_dir
    if args.task_algorithm is not None:
        config.TASK_ALLOCATION_CONFIG['algorithm'] = args.task_algorithm
    if args.path_algorithm is not None:
        config.PATH_PLANNING_CONFIG['algorithm'] = args.path_algorithm
    if args.reschedule_algorithm is not None:
        config.TASK_RESCHEDULING_CONFIG['algorithm'] = args.reschedule_algorithm

    # 保存配置到文件（如果指定）
    if args.save_config:
        config.save_to_file(args.save_config)
        print(f"配置已保存到: {args.save_config}")

    # 创建输出目录
    os.makedirs(config.OUTPUT_DIR, exist_ok=True)

    # 检查可视化模式
    if not VISUALIZATION_AVAILABLE and config.VISUALIZATION_MODE != 'none':
        print(f"警告: matplotlib库未安装，将使用文本模式运行仿真")
        config.VISUALIZATION_MODE = 'none'

    # 打印配置信息
    print(f"仿真配置:")
    print(f"  Case编号: {config.CASE_NUMBER}")
    print(f"  时间步长: {config.SIMULATION_TIME_STEP}s")
    print(f"  最大时间: {config.MAX_SIMULATION_TIME}s")
    print(f"  可视化模式: {config.VISUALIZATION_MODE}")
    print(f"  可视化速度: {config.VISUALIZATION_SPEED}x")
    print(f"  任务分配算法: {config.TASK_ALLOCATION_CONFIG['algorithm']}")
    print(f"  路径规划算法: {config.PATH_PLANNING_CONFIG['algorithm']}")
    print(f"  任务重调度算法: {config.TASK_RESCHEDULING_CONFIG['algorithm']}")

    # 创建仿真实例
    simulation = Simulation(sim_config=config)

    # 加载任务、障碍物和AGV
    simulation.load_tasks()
    simulation.load_obstacles()
    simulation.load_agvs()

    # 运行仿真
    performance = simulation.run()

    # 打印性能指标
    print("\n性能指标:")
    for metric, value in performance.items():
        print(f"{metric}: {value}")

if __name__ == "__main__":
    main()
