"""
AGV类定义 - 简化版本
使用匀速运动模型，不考虑电量约束和加速度约束
"""

class AGV:
    def __init__(self, agv_id, agv_type=None, position=None, speed=1.0):
        """
        初始化AGV实例
        
        Args:
            agv_id: AGV ID
            agv_type: AGV类型
            position: 初始位置（任务点ID或坐标）
            speed: AGV速度（单位：米/秒）
        """
        # AGV ID
        self.id = agv_id
        # AGV类型
        self.agv_type = agv_type
        # AGV状态：'idle'（空闲）, 'busy'（执行任务中）
        self.status = 'idle'
        # AGV当前位置（任务点ID或坐标）
        self.position = position
        # 目标位置
        self.target_position = None
        # 当前执行的任务
        self.current_task = None
        # AGV速度（单位：米/秒）
        self.speed = speed
        # 路径规划
        self.path = []
        # 当前路径索引
        self.path_index = 0

    def update_status(self):
        """更新AGV状态信息"""
        # 根据当前任务和位置更新状态
        if self.current_task:
            self.status = 'busy'
        else:
            self.status = 'idle'

    def update_position(self, new_position):
        """更新AGV位置
        
        Args:
            new_position: 新的位置（任务点ID或坐标）
        """
        self.position = new_position

    def assign_task(self, task):
        """分配任务给AGV
        
        Args:
            task: 任务对象
            
        Returns:
            bool: 是否成功分配任务
        """
        if self.status != 'idle':
            return False
        
        self.current_task = task
        task.assigned_agv = self.id
        task.is_assigned = True
        task.status = 'assigned'
        self.status = 'busy'
        self.target_position = task.start_point
        return True

    def complete_task(self):
        """完成当前任务"""
        if self.current_task:
            self.current_task.status = 'completed'
            self.current_task.is_completed = True
        self.current_task = None
        self.status = 'idle'
        self.target_position = None

    def get_info(self):
        """获取AGV信息
        
        Returns:
            dict: AGV的当前信息
        """
        return {
            'id': self.id,
            'agv_type': self.agv_type,
            'status': self.status,
            'position': self.position,
            'target_position': self.target_position,
            'current_task': self.current_task.id if self.current_task else None,
            'speed': self.speed
        }
