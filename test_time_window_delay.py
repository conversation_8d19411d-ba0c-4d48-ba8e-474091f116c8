#!/usr/bin/env python3
"""
Test script for time window-based delay calculation
测试基于时间窗口的延迟计算
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from transport_time_predictor import TransportTimePredictorInterface
import time


def test_time_interval_merging():
    """测试时间区间合并功能"""
    print("=== Testing Time Interval Merging ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        data_generator = predictor.data_generator
        
        # 测试不同的时间区间合并场景
        test_cases = [
            {
                'name': 'No intervals',
                'intervals': [],
                'expected': []
            },
            {
                'name': 'Single interval',
                'intervals': [(5.0, 10.0)],
                'expected': [(5.0, 10.0)]
            },
            {
                'name': 'Non-overlapping intervals',
                'intervals': [(1.0, 3.0), (5.0, 7.0), (10.0, 12.0)],
                'expected': [(1.0, 3.0), (5.0, 7.0), (10.0, 12.0)]
            },
            {
                'name': 'Overlapping intervals',
                'intervals': [(1.0, 5.0), (3.0, 8.0), (7.0, 10.0)],
                'expected': [(1.0, 10.0)]
            },
            {
                'name': 'Adjacent intervals',
                'intervals': [(1.0, 3.0), (3.0, 5.0), (7.0, 9.0)],
                'expected': [(1.0, 5.0), (7.0, 9.0)]
            },
            {
                'name': 'Complex overlapping',
                'intervals': [(1.0, 4.0), (2.0, 6.0), (8.0, 10.0), (9.0, 12.0)],
                'expected': [(1.0, 6.0), (8.0, 12.0)]
            }
        ]
        
        print("Testing time interval merging:")
        all_passed = True
        
        for case in test_cases:
            result = data_generator._merge_time_intervals(case['intervals'])
            expected = case['expected']
            
            print(f"\n  Case: {case['name']}")
            print(f"    Input: {case['intervals']}")
            print(f"    Result: {result}")
            print(f"    Expected: {expected}")
            
            if result == expected:
                print(f"    ✓ PASS")
            else:
                print(f"    ✗ FAIL")
                all_passed = False
        
        if all_passed:
            print("\n✓ All interval merging tests passed")
            return True
        else:
            print("\n⚠ Some interval merging tests failed")
            return False
        
    except Exception as e:
        print(f"✗ Time interval merging test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_time_window_finding():
    """测试时间窗口查找功能"""
    print("\n=== Testing Time Window Finding ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        data_generator = predictor.data_generator
        
        # 测试不同的时间窗口查找场景
        test_scenarios = [
            {
                'name': 'No obstacles - immediate start',
                'obstacles': [],
                'earliest_start': 0.0,
                'required_duration': 5.0,
                'expected': 0.0
            },
            {
                'name': 'Can start before first obstacle',
                'obstacles': [(10.0, 15.0)],
                'earliest_start': 0.0,
                'required_duration': 8.0,
                'expected': 0.0  # 0-8s, 障碍物10-15s
            },
            {
                'name': 'Cannot start before first obstacle',
                'obstacles': [(5.0, 10.0)],
                'earliest_start': 0.0,
                'required_duration': 8.0,
                'expected': 10.0  # 需要等到障碍物结束
            },
            {
                'name': 'Fit in gap between obstacles',
                'obstacles': [(2.0, 5.0), (12.0, 15.0)],
                'earliest_start': 0.0,
                'required_duration': 6.0,
                'expected': 5.0  # 在5-11s的间隙中
            },
            {
                'name': 'Gap too small - wait for last obstacle',
                'obstacles': [(2.0, 5.0), (8.0, 15.0)],
                'earliest_start': 0.0,
                'required_duration': 6.0,
                'expected': 15.0  # 间隙只有3s，不够6s
            },
            {
                'name': 'Multiple gaps - use first suitable',
                'obstacles': [(2.0, 4.0), (6.0, 8.0), (15.0, 20.0)],
                'earliest_start': 0.0,
                'required_duration': 3.0,
                'expected': 8.0  # 第一个间隙2s不够，第二个间隙7s够用
            }
        ]
        
        print("Testing time window finding:")
        all_passed = True
        
        for scenario in test_scenarios:
            result = data_generator._find_available_time_window(
                scenario['obstacles'],
                scenario['earliest_start'],
                scenario['required_duration']
            )
            
            print(f"\n  Scenario: {scenario['name']}")
            print(f"    Obstacles: {scenario['obstacles']}")
            print(f"    Required duration: {scenario['required_duration']}s")
            print(f"    Result: {result:.1f}s")
            print(f"    Expected: {scenario['expected']:.1f}s")
            
            if abs(result - scenario['expected']) <= 0.1:
                print(f"    ✓ PASS")
            else:
                print(f"    ✗ FAIL")
                all_passed = False
        
        if all_passed:
            print("\n✓ All time window finding tests passed")
            return True
        else:
            print("\n⚠ Some time window finding tests failed")
            return False
        
    except Exception as e:
        print(f"✗ Time window finding test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_multi_obstacle_delay_calculation():
    """测试多障碍物延迟计算"""
    print("\n=== Testing Multi-obstacle Delay Calculation ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        data_generator = predictor.data_generator
        
        # 测试路径：10米直线，AGV速度1m/s，需要10秒通过
        start_pos = {'x': 0.0, 'y': 0.0}
        end_pos = {'x': 10.0, 'y': 0.0}
        agv_speed = 1.0
        start_time = 0.0
        
        # 测试不同的多障碍物场景
        multi_obstacle_scenarios = [
            {
                'name': 'Two separate obstacles - can pass before first',
                'obstacles': [
                    (3.0, 0.0, 2.0, 2.0, 15.0, 5.0),  # 15-20s
                    (7.0, 0.0, 2.0, 2.0, 25.0, 5.0)   # 25-30s
                ],
                'expected_delay': 0.0  # 可以在15s前完成（0-10s）
            },
            {
                'name': 'Two overlapping obstacles',
                'obstacles': [
                    (3.0, 0.0, 2.0, 2.0, 5.0, 10.0),  # 5-15s
                    (7.0, 0.0, 2.0, 2.0, 12.0, 8.0)   # 12-20s，合并为5-20s
                ],
                'expected_delay': 20.0  # 等到20s开始
            },
            {
                'name': 'Three obstacles with usable gap',
                'obstacles': [
                    (2.0, 0.0, 2.0, 2.0, 2.0, 3.0),   # 2-5s
                    (5.0, 0.0, 2.0, 2.0, 8.0, 2.0),   # 8-10s
                    (8.0, 0.0, 2.0, 2.0, 25.0, 5.0)   # 25-30s
                ],
                'expected_delay': 10.0  # 在10s开始，10-20s通过
            },
            {
                'name': 'Continuous obstacles - must wait',
                'obstacles': [
                    (3.0, 0.0, 2.0, 2.0, 3.0, 5.0),   # 3-8s
                    (5.0, 0.0, 2.0, 2.0, 7.0, 6.0),   # 7-13s，合并为3-13s
                    (7.0, 0.0, 2.0, 2.0, 12.0, 8.0)   # 12-20s，合并为3-20s
                ],
                'expected_delay': 20.0  # 等到20s开始
            }
        ]
        
        print("Testing multi-obstacle delay calculation:")
        print(f"Path: 10m, Speed: {agv_speed}m/s, Travel time: 10s")
        
        for scenario in multi_obstacle_scenarios:
            delay = data_generator._calculate_path_delay(
                start_pos, end_pos, scenario['obstacles'], start_time, agv_speed
            )
            
            print(f"\n  Scenario: {scenario['name']}")
            print(f"    Obstacles:")
            for i, obs in enumerate(scenario['obstacles']):
                print(f"      {i+1}: {obs[4]:.1f}s - {obs[4] + obs[5]:.1f}s")
            print(f"    Calculated delay: {delay:.1f}s")
            print(f"    Expected delay: {scenario['expected_delay']:.1f}s")
            
            if abs(delay - scenario['expected_delay']) <= 1.0:
                print(f"    ✓ PASS")
            else:
                print(f"    ⚠ Deviation: {abs(delay - scenario['expected_delay']):.1f}s")
        
        print("\n✓ Multi-obstacle delay calculation tested")
        return True
        
    except Exception as e:
        print(f"✗ Multi-obstacle delay calculation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_speed_impact_on_windows():
    """测试AGV速度对时间窗口的影响"""
    print("\n=== Testing Speed Impact on Time Windows ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        data_generator = predictor.data_generator
        
        # 测试路径：12米直线
        start_pos = {'x': 0.0, 'y': 0.0}
        end_pos = {'x': 12.0, 'y': 0.0}
        start_time = 0.0
        
        # 固定障碍物：在5-8s和15-18s有障碍物，中间有7s间隙（8-15s）
        obstacles = [
            (4.0, 0.0, 2.0, 2.0, 5.0, 3.0),   # 5-8s
            (8.0, 0.0, 2.0, 2.0, 15.0, 3.0)   # 15-18s
        ]
        
        # 测试不同速度的影响
        speed_scenarios = [
            {'speed': 0.8, 'travel_time': 15.0, 'description': 'Slow AGV'},    # 15s通过，间隙不够
            {'speed': 1.0, 'travel_time': 12.0, 'description': 'Normal AGV'},  # 12s通过，间隙不够
            {'speed': 1.5, 'travel_time': 8.0, 'description': 'Fast AGV'},     # 8s通过，可能在第一个障碍物前
            {'speed': 2.0, 'travel_time': 6.0, 'description': 'Very fast AGV'} # 6s通过，可以在第一个障碍物前
        ]
        
        print("Testing speed impact on time windows:")
        print(f"Path: 12m")
        print(f"Obstacles: 5-8s and 15-18s (7s gap: 8-15s)")
        
        for scenario in speed_scenarios:
            agv_speed = scenario['speed']
            travel_time = scenario['travel_time']
            
            delay = data_generator._calculate_path_delay(
                start_pos, end_pos, obstacles, start_time, agv_speed
            )
            
            print(f"\n  {scenario['description']} ({agv_speed:.1f} m/s):")
            print(f"    Travel time: {travel_time:.1f}s")
            
            if travel_time <= 5.0:
                expected_behavior = "Can pass before first obstacle"
                expected_delay = 0.0
            elif travel_time <= 7.0:
                expected_behavior = "Can use gap between obstacles"
                expected_delay = 8.0  # 等到第一个障碍物结束
            else:
                expected_behavior = "Must wait for all obstacles"
                expected_delay = 18.0  # 等到最后一个障碍物结束
            
            print(f"    Expected: {expected_behavior}")
            print(f"    Calculated delay: {delay:.1f}s")
            print(f"    Expected delay: {expected_delay:.1f}s")
            
            if abs(delay - expected_delay) <= 1.0:
                print(f"    ✓ PASS")
            else:
                print(f"    ⚠ Deviation: {abs(delay - expected_delay):.1f}s")
        
        print("\n✓ Speed impact on time windows tested")
        return True
        
    except Exception as e:
        print(f"✗ Speed impact test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_complex_scenarios():
    """测试复杂场景"""
    print("\n=== Testing Complex Scenarios ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        data_generator = predictor.data_generator
        
        # 复杂场景：长路径，多个障碍物，不同间隙
        start_pos = {'x': 0.0, 'y': 0.0}
        end_pos = {'x': 20.0, 'y': 0.0}  # 20米路径
        agv_speed = 1.2  # 16.67秒通过
        start_time = 2.0  # 2秒开始
        
        complex_scenarios = [
            {
                'name': 'Dense obstacles with small gaps',
                'obstacles': [
                    (5.0, 0.0, 2.0, 2.0, 3.0, 4.0),   # 3-7s
                    (8.0, 0.0, 2.0, 2.0, 8.0, 3.0),   # 8-11s
                    (12.0, 0.0, 2.0, 2.0, 12.0, 5.0), # 12-17s
                    (16.0, 0.0, 2.0, 2.0, 20.0, 8.0)  # 20-28s
                ]
            },
            {
                'name': 'Long obstacles with big gaps',
                'obstacles': [
                    (5.0, 0.0, 2.0, 2.0, 5.0, 8.0),   # 5-13s
                    (15.0, 0.0, 2.0, 2.0, 25.0, 10.0) # 25-35s
                ]
            },
            {
                'name': 'Early and late obstacles',
                'obstacles': [
                    (3.0, 0.0, 2.0, 2.0, 1.0, 2.0),   # 1-3s
                    (17.0, 0.0, 2.0, 2.0, 30.0, 5.0)  # 30-35s
                ]
            }
        ]
        
        print("Testing complex scenarios:")
        print(f"Path: 20m, Speed: {agv_speed:.1f}m/s, Travel time: {20.0/agv_speed:.1f}s")
        print(f"Start time: {start_time}s")
        
        for scenario in complex_scenarios:
            delay = data_generator._calculate_path_delay(
                start_pos, end_pos, scenario['obstacles'], start_time, agv_speed
            )
            
            total_time = 20.0/agv_speed + delay
            actual_start_time = start_time + delay
            
            print(f"\n  Scenario: {scenario['name']}")
            print(f"    Obstacles:")
            for i, obs in enumerate(scenario['obstacles']):
                print(f"      {i+1}: {obs[4]:.1f}s - {obs[4] + obs[5]:.1f}s")
            print(f"    Calculated delay: {delay:.1f}s")
            print(f"    Actual start time: {actual_start_time:.1f}s")
            print(f"    Total time: {total_time:.1f}s")
        
        print("\n✓ Complex scenarios tested")
        return True
        
    except Exception as e:
        print(f"✗ Complex scenarios test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("Time Window-based Delay Calculation Test")
    print("=" * 40)
    
    tests = [
        ("Time Interval Merging", test_time_interval_merging),
        ("Time Window Finding", test_time_window_finding),
        ("Multi-obstacle Delay Calculation", test_multi_obstacle_delay_calculation),
        ("Speed Impact on Windows", test_speed_impact_on_windows),
        ("Complex Scenarios", test_complex_scenarios),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ Test '{test_name}' crashed: {e}")
            results[test_name] = False
    
    # 总结结果
    print(f"\n{'='*40}")
    print("Test Results Summary:")
    print(f"{'='*40}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<35} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed!")
        print("\nTime Window Features:")
        print("✓ Multi-obstacle interval merging")
        print("✓ Available time window detection")
        print("✓ Speed-dependent window analysis")
        print("✓ Complex scenario handling")
        print("✓ Optimal delay calculation")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
