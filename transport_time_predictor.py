"""
Transport Time Prediction Module using GNN + LSTM
基于GNN和LSTM架构的运输时间预测模块

序列模型:
(G_0, O_t, v, p_to, p_from, t_from, t_res)

G_0: 地图原始拓扑结构，无向图
O_t: 节点与边的障碍信息，数据格式(o, t_start, t_end)
v: AGV速度
p_to: 目标点
p_from: 起始点
t_from: AGV到达p_from的时间
t_res: 从p_from到p_to的运输时间

网络结构: GNN+LSTM
数据集构建: G_0用受支配数据，O_t随机生成，v固定值
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch_geometric
from torch_geometric.nn import GCNConv, GATConv, global_mean_pool
from torch_geometric.data import Data, DataLoader
import numpy as np
import random
import os
from typing import Dict, List, Tuple, Optional
import json
import pickle
from dataclasses import dataclass
from collections import defaultdict


@dataclass
class TransportSample:
    """运输时间预测样本"""
    graph_data: Data  # 图数据
    obstacles: List[Tuple]  # 障碍物信息 (node_id, t_start, t_end)
    agv_speed: float  # AGV速度
    start_point: int  # 起始点ID
    end_point: int  # 目标点ID
    start_time: float  # AGV到达起始点的时间
    actual_time: float  # 实际运输时间


class GraphEncoder(nn.Module):
    """图神经网络编码器"""
    
    def __init__(self, node_features: int, edge_features: int, hidden_dim: int, num_layers: int = 3):
        super(GraphEncoder, self).__init__()
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        # 节点特征编码
        self.node_encoder = nn.Linear(node_features, hidden_dim)
        
        # 边特征编码
        self.edge_encoder = nn.Linear(edge_features, hidden_dim)
        
        # GCN层
        self.gcn_layers = nn.ModuleList([
            GCNConv(hidden_dim, hidden_dim) for _ in range(num_layers)
        ])
        
        # 注意力层
        self.attention = GATConv(hidden_dim, hidden_dim, heads=4, concat=False)
        
        # 输出层
        self.output_proj = nn.Linear(hidden_dim, hidden_dim)
        
    def forward(self, x, edge_index, edge_attr, batch=None):
        """
        前向传播
        
        Args:
            x: 节点特征 [num_nodes, node_features]
            edge_index: 边索引 [2, num_edges]
            edge_attr: 边特征 [num_edges, edge_features]
            batch: 批次信息
        """
        # 编码节点特征
        x = F.relu(self.node_encoder(x))
        
        # GCN层
        for gcn in self.gcn_layers:
            x = F.relu(gcn(x, edge_index))
            x = F.dropout(x, training=self.training)
        
        # 注意力层
        x = F.relu(self.attention(x, edge_index))
        
        # 输出投影
        x = self.output_proj(x)
        
        return x


class TemporalEncoder(nn.Module):
    """时序编码器 (LSTM)"""
    
    def __init__(self, input_dim: int, hidden_dim: int, num_layers: int = 2):
        super(TemporalEncoder, self).__init__()
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        # LSTM层
        self.lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=0.1 if num_layers > 1 else 0
        )
        
        # 输出层
        self.output_proj = nn.Linear(hidden_dim, hidden_dim)
        
    def forward(self, x, lengths=None):
        """
        前向传播
        
        Args:
            x: 输入序列 [batch_size, seq_len, input_dim]
            lengths: 序列长度
        """
        # LSTM前向传播
        lstm_out, (hidden, cell) = self.lstm(x)
        
        # 使用最后一个时间步的输出
        if lengths is not None:
            # 根据实际长度选择输出
            batch_size = x.size(0)
            last_outputs = []
            for i, length in enumerate(lengths):
                last_outputs.append(lstm_out[i, length-1, :])
            output = torch.stack(last_outputs)
        else:
            output = lstm_out[:, -1, :]  # [batch_size, hidden_dim]
        
        # 输出投影
        output = self.output_proj(output)
        
        return output


class TransportTimePredictor(nn.Module):
    """运输时间预测器 (GNN + LSTM)"""
    
    def __init__(self, config: Dict):
        super(TransportTimePredictor, self).__init__()
        self.config = config
        
        # 图编码器
        self.graph_encoder = GraphEncoder(
            node_features=config['node_features'],
            edge_features=config['edge_features'],
            hidden_dim=config['graph_hidden_dim'],
            num_layers=config['graph_layers']
        )
        
        # 时序编码器
        temporal_input_dim = (
            config['graph_hidden_dim'] * 2 +  # 起始点和目标点的图嵌入
            config['obstacle_features'] +      # 障碍物特征
            3  # AGV速度、起始时间、距离
        )
        
        self.temporal_encoder = TemporalEncoder(
            input_dim=temporal_input_dim,
            hidden_dim=config['temporal_hidden_dim'],
            num_layers=config['temporal_layers']
        )
        
        # 预测头
        self.predictor = nn.Sequential(
            nn.Linear(config['temporal_hidden_dim'], config['temporal_hidden_dim'] // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(config['temporal_hidden_dim'] // 2, 1)
        )
        
    def forward(self, batch_data):
        """
        前向传播
        
        Args:
            batch_data: 批次数据
        """
        batch_size = len(batch_data)
        device = next(self.parameters()).device
        
        # 处理图数据
        graph_embeddings = []
        temporal_features = []
        
        for sample in batch_data:
            # 图编码
            graph_data = sample['graph_data'].to(device)
            node_embeddings = self.graph_encoder(
                graph_data.x,
                graph_data.edge_index,
                graph_data.edge_attr
            )
            
            # 提取起始点和目标点的嵌入
            start_embedding = node_embeddings[sample['start_point']]
            end_embedding = node_embeddings[sample['end_point']]
            
            # 构建时序特征
            temporal_feature = torch.cat([
                start_embedding,
                end_embedding,
                sample['obstacle_features'].to(device),
                torch.tensor([
                    sample['agv_speed'],
                    sample['start_time'],
                    sample['distance']
                ], dtype=torch.float32, device=device)
            ])
            
            temporal_features.append(temporal_feature)
        
        # 堆叠时序特征
        temporal_input = torch.stack(temporal_features).unsqueeze(1)  # [batch_size, 1, feature_dim]
        
        # 时序编码
        temporal_output = self.temporal_encoder(temporal_input)
        
        # 预测运输时间
        predicted_time = self.predictor(temporal_output)
        
        return predicted_time.squeeze(-1)  # [batch_size]


class TransportDataGenerator:
    """运输时间数据生成器"""
    
    def __init__(self, graph_structure: Dict, config: Dict, case_number: int = 1, case_config: Dict = None):
        self.graph_structure = graph_structure
        self.config = config
        self.task_points = graph_structure['task_points']
        self.channels = graph_structure['channels']
        self.case_number = case_number
        self.case_config = case_config

        # 构建邻接矩阵和距离矩阵
        self._build_graph_matrices()

        # 加载障碍物数据
        self.obstacles_data = self._load_obstacles_data()

        # 提取CASE配置参数
        if case_config:
            self.task_generation_params = case_config.get('task_generation_params', {})
            self.agv_params = case_config.get('agv_params', {})
        else:
            self.task_generation_params = {}
            self.agv_params = {}
        
    def _build_graph_matrices(self):
        """构建图的邻接矩阵和距离矩阵"""
        num_points = len(self.task_points)
        self.adjacency_matrix = np.zeros((num_points, num_points))
        self.distance_matrix = np.full((num_points, num_points), np.inf)
        
        # 点ID到索引的映射
        self.point_to_idx = {point_id: idx for idx, point_id in enumerate(self.task_points.keys())}
        self.idx_to_point = {idx: point_id for point_id, idx in self.point_to_idx.items()}
        
        # 填充邻接矩阵和距离矩阵
        for (start, end), channel_info in self.channels.items():
            if start in self.task_points and end in self.task_points:
                start_idx = self.point_to_idx[start]
                end_idx = self.point_to_idx[end]
                
                # 计算欧几里得距离
                start_pos = self.task_points[start]
                end_pos = self.task_points[end]
                distance = np.sqrt((start_pos['x'] - end_pos['x'])**2 + 
                                 (start_pos['y'] - end_pos['y'])**2)
                
                self.adjacency_matrix[start_idx, end_idx] = 1
                self.adjacency_matrix[end_idx, start_idx] = 1
                self.distance_matrix[start_idx, end_idx] = distance
                self.distance_matrix[end_idx, start_idx] = distance
        
        # 对角线设为0
        np.fill_diagonal(self.distance_matrix, 0)

    def _load_obstacles_data(self) -> List[Dict]:
        """加载障碍物数据文件"""
        import os

        # 构建障碍物文件路径
        obstacles_file = f"output/obstacles/obstacles_case{self.case_number}.json"

        if not os.path.exists(obstacles_file):
            print(f"警告: 障碍物文件 {obstacles_file} 不存在，将使用空障碍物列表")
            return []

        try:
            with open(obstacles_file, 'r', encoding='utf-8') as f:
                obstacles_data = json.load(f)
            print(f"成功加载 {len(obstacles_data)} 个障碍物从 {obstacles_file}")
            return obstacles_data
        except Exception as e:
            print(f"加载障碍物文件失败: {e}")
            return []
        
    def get_obstacles_in_timeframe(self, start_time: float, end_time: float) -> List[Tuple]:
        """获取指定时间范围内的障碍物（直接从文件读取）"""
        obstacles = []

        for obs_data in self.obstacles_data:
            # 提取障碍物信息
            center_x = obs_data['center']['x']
            center_y = obs_data['center']['y']
            width = obs_data['size']['width']
            height = obs_data['size']['height']
            insert_time = obs_data['insert_time']
            duration_minutes = obs_data['duration_minutes']
            duration = duration_minutes * 60  # 转换为秒

            # 检查障碍物是否在指定时间范围内
            obs_start = insert_time
            obs_end = insert_time + duration

            # 如果障碍物与时间范围有重叠，则包含该障碍物
            if not (obs_end < start_time or obs_start > end_time):
                obstacles.append((center_x, center_y, width, height, insert_time, duration))

        return obstacles

    def get_all_obstacles(self) -> List[Tuple]:
        """获取所有障碍物"""
        obstacles = []

        for obs_data in self.obstacles_data:
            center_x = obs_data['center']['x']
            center_y = obs_data['center']['y']
            width = obs_data['size']['width']
            height = obs_data['size']['height']
            insert_time = obs_data['insert_time']
            duration_minutes = obs_data['duration_minutes']
            duration = duration_minutes * 60  # 转换为秒

            obstacles.append((center_x, center_y, width, height, insert_time, duration))

        return obstacles

    def _normal_random(self, mean: float, std: float) -> float:
        """生成满足高斯分布的随机数（与DO_generator.py保持一致）"""
        import math
        # Box-Muller变换
        u1 = random.random()
        u2 = random.random()
        z0 = math.sqrt(-2.0 * math.log(u1)) * math.cos(2.0 * math.pi * u2)
        return mean + z0 * std

    def create_graph_data(self, obstacles: List[Tuple]) -> Data:
        """创建图数据"""
        num_nodes = len(self.task_points)

        # 节点特征: [x, y, has_obstacle, obstacle_start, obstacle_end]
        node_features = []
        for point_id, point_info in self.task_points.items():
            # 基础位置特征
            features = [point_info['x'], point_info['y']]

            # 障碍物特征 - 检查是否有障碍物覆盖该点
            has_obstacle = 0
            obstacle_start = 0
            obstacle_end = 0

            point_x, point_y = point_info['x'], point_info['y']

            # 检查每个障碍物是否覆盖当前点
            for center_x, center_y, width, height, insert_time, duration in obstacles:
                # 计算障碍物边界
                left = center_x - width / 2
                right = center_x + width / 2
                top = center_y - height / 2
                bottom = center_y + height / 2

                # 检查点是否在障碍物范围内
                if left <= point_x <= right and top <= point_y <= bottom:
                    has_obstacle = 1
                    obstacle_start = insert_time
                    obstacle_end = insert_time + duration
                    break

            features.extend([has_obstacle, obstacle_start, obstacle_end])
            node_features.append(features)

        # 边索引和边特征
        edge_indices = []
        edge_features = []

        for (start, end), channel_info in self.channels.items():
            if start in self.task_points and end in self.task_points:
                start_idx = self.point_to_idx[start]
                end_idx = self.point_to_idx[end]

                # 双向边
                edge_indices.extend([[start_idx, end_idx], [end_idx, start_idx]])

                # 边特征: [distance, has_obstacle_on_path]
                distance = self.distance_matrix[start_idx, end_idx]
                has_path_obstacle = self._check_path_obstacle(start_idx, end_idx, obstacles)

                edge_feature = [distance, has_path_obstacle]
                edge_features.extend([edge_feature, edge_feature])

        # 转换为张量
        x = torch.tensor(node_features, dtype=torch.float32)
        edge_index = torch.tensor(edge_indices, dtype=torch.long).t().contiguous()
        edge_attr = torch.tensor(edge_features, dtype=torch.float32)

        return Data(x=x, edge_index=edge_index, edge_attr=edge_attr)

    def _check_path_obstacle(self, start_idx: int, end_idx: int, obstacles: List[Tuple]) -> float:
        """检查路径上是否有障碍物"""
        # 获取起点和终点坐标
        start_point_id = list(self.task_points.keys())[start_idx]
        end_point_id = list(self.task_points.keys())[end_idx]
        start_pos = self.task_points[start_point_id]
        end_pos = self.task_points[end_point_id]

        # 检查障碍物是否影响路径
        for center_x, center_y, width, height, insert_time, duration in obstacles:
            # 简化检查：如果障碍物与起点或终点重叠
            left = center_x - width / 2
            right = center_x + width / 2
            top = center_y - height / 2
            bottom = center_y + height / 2

            # 检查起点或终点是否在障碍物范围内
            if ((left <= start_pos['x'] <= right and top <= start_pos['y'] <= bottom) or
                (left <= end_pos['x'] <= right and top <= end_pos['y'] <= bottom)):
                return 1.0

        return 0.0

    def calculate_actual_time(self, start_idx: int, end_idx: int, agv_speed: float,
                            start_time: float, obstacles: List[Tuple]) -> float:
        """计算实际运输时间（考虑障碍物影响）"""
        base_distance = self.distance_matrix[start_idx, end_idx]

        # 检查距离是否有效
        if np.isinf(base_distance) or base_distance <= 0:
            # 如果没有直接路径，使用简单的曼哈顿距离估算
            start_point = list(self.task_points.keys())[start_idx]
            end_point = list(self.task_points.keys())[end_idx]
            start_pos = self.task_points[start_point]
            end_pos = self.task_points[end_point]
            base_distance = abs(start_pos['x'] - end_pos['x']) + abs(start_pos['y'] - end_pos['y'])

        base_time = base_distance / agv_speed

        # 计算障碍物延迟
        delay = 0.0

        # 获取起点和终点坐标
        start_point_id = list(self.task_points.keys())[start_idx]
        end_point_id = list(self.task_points.keys())[end_idx]
        start_pos = self.task_points[start_point_id]
        end_pos = self.task_points[end_point_id]

        for center_x, center_y, width, height, insert_time, duration in obstacles:
            # 计算障碍物边界
            left = center_x - width / 2
            right = center_x + width / 2
            top = center_y - height / 2
            bottom = center_y + height / 2

            # 检查起点或终点是否被障碍物覆盖
            start_blocked = (left <= start_pos['x'] <= right and top <= start_pos['y'] <= bottom)
            end_blocked = (left <= end_pos['x'] <= right and top <= end_pos['y'] <= bottom)

            if start_blocked or end_blocked:
                # 障碍物时间窗口
                t_start = insert_time
                t_end = insert_time + duration

                # AGV运输时间窗口
                arrival_time = start_time
                departure_time = start_time + base_time

                if not (departure_time < t_start or arrival_time > t_end):
                    # 有重叠，计算延迟
                    if arrival_time < t_start:
                        # AGV需要等待障碍物消失
                        delay += t_end - arrival_time
                    else:
                        # AGV到达时障碍物还在
                        delay += t_end - arrival_time

        total_time = base_time + delay

        # 确保返回值是有效的
        if np.isnan(total_time) or np.isinf(total_time) or total_time <= 0:
            total_time = base_distance / agv_speed  # 回退到基础时间

        return max(0.1, total_time)  # 最小时间为0.1秒

    def generate_sample(self) -> Dict:
        """生成一个训练样本（基于CASE配置）"""
        # 根据CASE配置选择起始点和目标点
        start_point_id, end_point_id = self._select_task_points_from_case()

        start_idx = self.point_to_idx[start_point_id]
        end_idx = self.point_to_idx[end_point_id]

        # 根据CASE配置生成AGV参数
        agv_speed = self._select_agv_speed_from_case()
        start_time = random.uniform(0, 300)   # 0-300秒的时间范围

        # 计算基础距离和预估运输时间
        distance = self.distance_matrix[start_idx, end_idx]
        if np.isinf(distance) or distance <= 0:
            # 如果没有直接路径，使用简单的曼哈顿距离估算
            start_pos = self.task_points[start_point_id]
            end_pos = self.task_points[end_point_id]
            distance = abs(start_pos['x'] - end_pos['x']) + abs(start_pos['y'] - end_pos['y'])

        estimated_travel_time = distance / agv_speed
        end_time = start_time + estimated_travel_time + 60  # 增加缓冲时间

        # 获取相关时间范围内的障碍物
        obstacles = self.get_obstacles_in_timeframe(start_time, end_time)

        # 创建图数据
        graph_data = self.create_graph_data(obstacles)

        # 计算实际运输时间
        actual_time = self.calculate_actual_time(start_idx, end_idx, agv_speed, start_time, obstacles)

        # 构建障碍物特征向量
        obstacle_features = self._encode_obstacles(obstacles, start_time, estimated_travel_time)

        return {
            'graph_data': graph_data,
            'start_point': start_idx,
            'end_point': end_idx,
            'agv_speed': agv_speed,
            'start_time': start_time,
            'distance': distance,
            'obstacle_features': obstacle_features,
            'actual_time': actual_time
        }

    def _select_task_points_from_case(self) -> Tuple[str, str]:
        """根据CASE配置选择任务起始点和目标点"""
        if not self.task_generation_params or 'task_types' not in self.task_generation_params:
            # 如果没有CASE配置，随机选择
            point_ids = list(self.task_points.keys())
            start_point = random.choice(point_ids)
            end_point = random.choice([p for p in point_ids if p != start_point])
            return start_point, end_point

        # 根据任务类型比例随机选择任务类型
        task_types = self.task_generation_params['task_types']
        type_names = list(task_types.keys())
        type_ratios = [task_types[name]['ratio'] for name in type_names]

        # 归一化比例
        total_ratio = sum(type_ratios)
        type_probs = [r / total_ratio for r in type_ratios]

        # 选择任务类型
        selected_type = np.random.choice(type_names, p=type_probs)
        task_config = task_types[selected_type]

        # 选择起始点
        start_point = self._select_point_from_config(task_config['start_point'])

        # 选择目标点
        end_point = self._select_point_from_config(task_config['end_point'])

        # 确保起始点和目标点不同
        if start_point == end_point:
            available_points = list(self.task_points.keys())
            end_point = random.choice([p for p in available_points if p != start_point])

        return start_point, end_point

    def _select_point_from_config(self, point_config: Dict) -> str:
        """根据配置选择点"""
        if point_config['mode'] == 'uniform':
            return random.choice(point_config['candidates'])
        elif point_config['mode'] == 'weighted':
            points = list(point_config['distribution'].keys())
            weights = list(point_config['distribution'].values())
            # 归一化权重
            total_weight = sum(weights)
            probs = [w / total_weight for w in weights]
            return np.random.choice(points, p=probs)
        else:
            # 默认随机选择
            return random.choice(list(self.task_points.keys()))

    def _select_agv_speed_from_case(self) -> float:
        """根据CASE配置选择AGV速度"""
        if not self.agv_params or 'agv_types' not in self.agv_params:
            # 如果没有CASE配置，使用默认范围
            return random.uniform(0.5, 2.0)

        # 根据AGV类型比例随机选择AGV类型
        agv_types = self.agv_params['agv_types']
        type_names = list(agv_types.keys())
        type_ratios = [agv_types[name]['ratio'] for name in type_names]

        # 归一化比例
        total_ratio = sum(type_ratios)
        type_probs = [r / total_ratio for r in type_ratios]

        # 选择AGV类型
        selected_type = np.random.choice(type_names, p=type_probs)
        agv_config = agv_types[selected_type]

        return agv_config['speed']

    def _encode_obstacles(self, obstacles: List[Tuple], start_time: float, duration: float) -> torch.Tensor:
        """编码障碍物特征"""
        # 固定长度的障碍物特征向量
        max_obstacles = 10
        feature_dim = 6  # [center_x, center_y, width, height, t_start, t_end]

        obstacle_features = torch.zeros(max_obstacles * feature_dim)

        # 按时间相关性排序障碍物
        relevant_obstacles = []
        end_time = start_time + duration

        for center_x, center_y, width, height, insert_time, obs_duration in obstacles:
            t_start = insert_time
            t_end = insert_time + obs_duration

            # 计算与当前运输任务的时间相关性
            relevance = 0.0
            if not (end_time < t_start or start_time > t_end):
                # 时间重叠
                overlap = min(end_time, t_end) - max(start_time, t_start)
                relevance = overlap / duration

            relevant_obstacles.append((center_x, center_y, width, height, t_start, t_end, relevance))

        # 按相关性排序
        relevant_obstacles.sort(key=lambda x: x[6], reverse=True)

        # 填充特征向量
        for i, (center_x, center_y, width, height, t_start, t_end, relevance) in enumerate(relevant_obstacles[:max_obstacles]):
            base_idx = i * feature_dim
            obstacle_features[base_idx:base_idx + feature_dim] = torch.tensor([
                center_x / 20.0,    # 归一化中心X坐标
                center_y / 20.0,    # 归一化中心Y坐标
                width / 5.0,        # 归一化宽度
                height / 5.0,       # 归一化高度
                t_start / 200.0,    # 归一化开始时间
                t_end / 200.0       # 归一化结束时间
            ])

        return obstacle_features

    def generate_dataset(self, num_samples: int) -> List[Dict]:
        """生成数据集"""
        dataset = []
        for _ in range(num_samples):
            sample = self.generate_sample()
            dataset.append(sample)
        return dataset


class TransportTimeTrainer:
    """运输时间预测器训练器"""

    def __init__(self, model: TransportTimePredictor, config: Dict):
        self.model = model
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model.to(self.device)

        # 优化器和损失函数
        self.optimizer = torch.optim.Adam(model.parameters(), lr=config['learning_rate'])
        self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', patience=10, factor=0.5
        )
        self.criterion = nn.MSELoss()

    def train_epoch(self, train_loader) -> float:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0

        for batch_data in train_loader:
            self.optimizer.zero_grad()

            # 前向传播
            predictions = self.model(batch_data)
            targets = torch.tensor([sample['actual_time'] for sample in batch_data],
                                 dtype=torch.float32, device=self.device)

            # 计算损失
            loss = self.criterion(predictions, targets)

            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()

            total_loss += loss.item()
            num_batches += 1

        return total_loss / num_batches

    def validate(self, val_loader) -> Tuple[float, float]:
        """验证模型"""
        self.model.eval()
        total_loss = 0.0
        total_mae = 0.0
        num_batches = 0

        with torch.no_grad():
            for batch_data in val_loader:
                predictions = self.model(batch_data)
                targets = torch.tensor([sample['actual_time'] for sample in batch_data],
                                     dtype=torch.float32, device=self.device)

                loss = self.criterion(predictions, targets)
                mae = torch.mean(torch.abs(predictions - targets))

                total_loss += loss.item()
                total_mae += mae.item()
                num_batches += 1

        return total_loss / num_batches, total_mae / num_batches

    def train(self, train_dataset: List[Dict], val_dataset: List[Dict],
              num_epochs: int, batch_size: int = 32):
        """训练模型"""
        # 创建数据加载器
        train_loader = self._create_dataloader(train_dataset, batch_size, shuffle=True)
        val_loader = self._create_dataloader(val_dataset, batch_size, shuffle=False)

        best_val_loss = float('inf')
        patience_counter = 0

        for epoch in range(num_epochs):
            # 训练
            train_loss = self.train_epoch(train_loader)

            # 验证
            val_loss, val_mae = self.validate(val_loader)

            # 学习率调度
            self.scheduler.step(val_loss)

            print(f"Epoch {epoch+1}/{num_epochs}")
            print(f"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}, Val MAE: {val_mae:.4f}")

            # 早停
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # 保存最佳模型
                torch.save(self.model.state_dict(), 'best_transport_predictor.pth')
            else:
                patience_counter += 1
                if patience_counter >= 20:
                    print("Early stopping triggered")
                    break

    def _create_dataloader(self, dataset: List[Dict], batch_size: int, shuffle: bool):
        """创建数据加载器"""
        # 过滤掉无效的样本
        valid_dataset = []
        for sample in dataset:
            if (sample['actual_time'] > 0 and
                not np.isnan(sample['actual_time']) and
                not np.isinf(sample['actual_time'])):
                valid_dataset.append(sample)

        print(f"Filtered dataset: {len(valid_dataset)}/{len(dataset)} valid samples")

        # 简单的批次生成器
        def batch_generator():
            if shuffle:
                random.shuffle(valid_dataset)

            for i in range(0, len(valid_dataset), batch_size):
                yield valid_dataset[i:i + batch_size]

        return batch_generator()


def create_default_config() -> Dict:
    """创建默认配置"""
    return {
        'node_features': 5,  # [x, y, has_obstacle, obstacle_start, obstacle_end]
        'edge_features': 2,  # [distance, has_obstacle_on_path]
        'obstacle_features': 60,  # 10 obstacles * 6 features each
        'graph_hidden_dim': 64,
        'graph_layers': 3,
        'temporal_hidden_dim': 128,
        'temporal_layers': 2,
        'learning_rate': 0.001,
        'batch_size': 32,
        'num_epochs': 100
    }


def load_case_config(case_number: int = 1) -> Dict:
    """加载CASE配置文件"""
    import sys
    import importlib.util

    # 构建CASE配置文件路径
    case_file = f"ENV_CONFIG/CASE{case_number}.py"

    if not os.path.exists(case_file):
        print(f"警告: CASE配置文件 {case_file} 不存在，使用默认配置")
        return create_sample_graph_structure()

    try:
        # 动态导入CASE配置模块
        spec = importlib.util.spec_from_file_location(f"CASE{case_number}", case_file)
        case_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(case_module)

        # 提取配置信息
        config = {
            'task_points': case_module.task_points,
            'channels': case_module.channels,
            'task_generation_params': case_module.task_generation_params,
            'dynamic_obstacle_params': case_module.dynamic_obstacle_params,
            'agv_params': case_module.agv_params
        }

        print(f"成功加载CASE{case_number}配置")
        return config

    except Exception as e:
        print(f"加载CASE配置失败: {e}")
        return create_sample_graph_structure()


def load_graph_structure(config_path: str = None, case_number: int = 1) -> Dict:
    """加载图结构数据"""
    if config_path is None:
        # 使用CASE配置文件
        return load_case_config(case_number)
    else:
        with open(config_path, 'r') as f:
            return json.load(f)


def create_sample_graph_structure() -> Dict:
    """创建示例图结构数据"""
    # 创建一个简单的网格图结构用于测试
    task_points = {}
    channels = {}

    # 创建5x5网格的任务点
    for i in range(5):
        for j in range(5):
            point_id = f"P{i*5 + j + 1}"
            task_points[point_id] = {'x': i * 2, 'y': j * 2}

    # 创建网格连接
    for i in range(5):
        for j in range(5):
            current_id = f"P{i*5 + j + 1}"

            # 水平连接
            if j < 4:
                next_id = f"P{i*5 + j + 2}"
                channels[(current_id, next_id)] = {'length': 2.0}

            # 垂直连接
            if i < 4:
                next_id = f"P{(i+1)*5 + j + 1}"
                channels[(current_id, next_id)] = {'length': 2.0}

    return {
        'task_points': task_points,
        'channels': channels
    }


class TransportTimePredictorInterface:
    """运输时间预测器接口"""

    def __init__(self, model_path: str = None, config_path: str = None, case_number: int = 1):
        self.config = create_default_config()
        self.case_number = case_number

        # 加载CASE配置
        self.case_config = load_case_config(case_number)

        # 从CASE配置中提取图结构
        self.graph_structure = {
            'task_points': self.case_config['task_points'],
            'channels': self.case_config['channels']
        }

        # 创建模型
        self.model = TransportTimePredictor(self.config)

        # 加载预训练模型
        if model_path and os.path.exists(model_path):
            self.model.load_state_dict(torch.load(model_path, map_location='cpu'))
            self.model.eval()

        # 创建数据生成器
        self.data_generator = TransportDataGenerator(self.graph_structure, self.config, case_number, self.case_config)

        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model.to(self.device)

    def predict_transport_time(self, start_point: str, end_point: str, agv_speed: float,
                             start_time: float, obstacles: List[Tuple] = None) -> float:
        """
        预测运输时间

        Args:
            start_point: 起始点ID
            end_point: 目标点ID
            agv_speed: AGV速度
            start_time: 开始时间
            obstacles: 障碍物列表 [(node_id, t_start, t_end), ...]

        Returns:
            预测的运输时间
        """
        if obstacles is None:
            obstacles = []

        # 转换点ID到索引
        start_idx = self.data_generator.point_to_idx[start_point]
        end_idx = self.data_generator.point_to_idx[end_point]

        # 转换障碍物节点ID到索引
        converted_obstacles = []
        for obs in obstacles:
            if isinstance(obs[0], str):
                obs_idx = self.data_generator.point_to_idx[obs[0]]
                converted_obstacles.append((obs_idx, obs[1], obs[2]))
            else:
                converted_obstacles.append(obs)

        # 创建图数据
        graph_data = self.data_generator.create_graph_data(converted_obstacles)

        # 计算距离
        distance = self.data_generator.distance_matrix[start_idx, end_idx]

        # 编码障碍物特征
        base_time = distance / agv_speed
        obstacle_features = self.data_generator._encode_obstacles(
            converted_obstacles, start_time, base_time
        )

        # 构建输入样本
        sample = {
            'graph_data': graph_data,
            'start_point': start_idx,
            'end_point': end_idx,
            'agv_speed': agv_speed,
            'start_time': start_time,
            'distance': distance,
            'obstacle_features': obstacle_features,
            'actual_time': 0  # 占位符
        }

        # 预测
        self.model.eval()
        with torch.no_grad():
            prediction = self.model([sample])
            return prediction.item()

    def train_model(self, num_train_samples: int = 10000, num_val_samples: int = 2000):
        """训练模型"""
        print("Generating training data...")
        train_dataset = self.data_generator.generate_dataset(num_train_samples)

        print("Generating validation data...")
        val_dataset = self.data_generator.generate_dataset(num_val_samples)

        print("Starting training...")
        trainer = TransportTimeTrainer(self.model, self.config)
        trainer.train(
            train_dataset,
            val_dataset,
            num_epochs=self.config['num_epochs'],
            batch_size=self.config['batch_size']
        )

        print("Training completed!")

    def save_model(self, path: str):
        """保存模型"""
        torch.save(self.model.state_dict(), path)
        print(f"Model saved to {path}")

    def evaluate_model(self, test_samples: int = 1000) -> Dict:
        """评估模型性能"""
        print("Generating test data...")
        test_dataset = self.data_generator.generate_dataset(test_samples)

        predictions = []
        actuals = []

        self.model.eval()
        with torch.no_grad():
            for sample in test_dataset:
                pred = self.model([sample])
                predictions.append(pred.item())
                actuals.append(sample['actual_time'])

        predictions = np.array(predictions)
        actuals = np.array(actuals)

        # 计算评估指标
        mae = np.mean(np.abs(predictions - actuals))
        mse = np.mean((predictions - actuals) ** 2)
        rmse = np.sqrt(mse)
        mape = np.mean(np.abs((predictions - actuals) / actuals)) * 100

        return {
            'mae': mae,
            'mse': mse,
            'rmse': rmse,
            'mape': mape,
            'num_samples': test_samples
        }


def main():
    """主函数 - 演示使用"""
    import os

    print("Transport Time Predictor - GNN + LSTM")
    print("=====================================")

    # 创建预测器
    predictor = TransportTimePredictorInterface()

    # 训练模型
    print("\n1. Training model...")
    predictor.train_model(num_train_samples=5000, num_val_samples=1000)

    # 保存模型
    model_path = "transport_time_predictor.pth"
    predictor.save_model(model_path)

    # 评估模型
    print("\n2. Evaluating model...")
    metrics = predictor.evaluate_model(test_samples=500)
    print("Evaluation Results:")
    for metric, value in metrics.items():
        print(f"  {metric}: {value:.4f}")

    # 示例预测
    print("\n3. Example predictions...")

    # 无障碍物情况
    pred_time_1 = predictor.predict_transport_time(
        start_point='P1',
        end_point='P10',
        agv_speed=1.0,
        start_time=0.0,
        obstacles=[]
    )
    print(f"No obstacles: P1 -> P10, speed=1.0 m/s, predicted time: {pred_time_1:.2f}s")

    # 有障碍物情况
    obstacles = [('P5', 10.0, 25.0), ('P8', 15.0, 30.0)]
    pred_time_2 = predictor.predict_transport_time(
        start_point='P1',
        end_point='P10',
        agv_speed=1.0,
        start_time=0.0,
        obstacles=obstacles
    )
    print(f"With obstacles: P1 -> P10, speed=1.0 m/s, predicted time: {pred_time_2:.2f}s")

    print("\nDemo completed!")


if __name__ == "__main__":
    main()
