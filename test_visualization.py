#!/usr/bin/env python3
"""
Test script for visualization interface
Tests different visualization modes and configurations
"""

import subprocess
import sys
import time

def run_test(description, command, wait_time=10):
    """Run a test with given description and command"""
    print(f"\n{'='*60}")
    print(f"TEST: {description}")
    print(f"COMMAND: {command}")
    print(f"{'='*60}")
    
    try:
        # Run the command
        process = subprocess.Popen(
            command.split(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for specified time or until process completes
        try:
            stdout, stderr = process.communicate(timeout=wait_time)
            print("STDOUT:")
            print(stdout)
            if stderr:
                print("STDERR:")
                print(stderr)
        except subprocess.TimeoutExpired:
            print(f"Test ran for {wait_time} seconds, terminating...")
            process.terminate()
            try:
                stdout, stderr = process.communicate(timeout=5)
                print("STDOUT:")
                print(stdout)
                if stderr:
                    print("STDERR:")
                    print(stderr)
            except subprocess.TimeoutExpired:
                process.kill()
                print("Process killed due to timeout")
        
        print(f"Test completed: {description}")
        
    except Exception as e:
        print(f"Error running test: {e}")

def main():
    """Main test function"""
    print("Visualization Interface Test Suite")
    print("Testing different visualization modes and configurations")
    
    # Test 1: Fast mode with real-time visualization
    run_test(
        "Fast mode with real-time visualization",
        "python run_simulation.py --config-template fast --case 1 --visualization real-time --max-time 30",
        wait_time=15
    )
    
    # Test 2: Debug mode with step visualization
    run_test(
        "Debug mode with step visualization",
        "python run_simulation.py --config-template debug --case 1 --visualization step --max-time 20",
        wait_time=10
    )
    
    # Test 3: Production mode (no visualization)
    run_test(
        "Production mode (no visualization)",
        "python run_simulation.py --config-template production --case 1 --max-time 30",
        wait_time=10
    )
    
    # Test 4: Custom configuration
    run_test(
        "Custom configuration with fast visualization",
        "python run_simulation.py --case 2 --visualization fast --speed 20 --max-time 60",
        wait_time=15
    )
    
    # Test 5: Final result mode
    run_test(
        "Final result visualization mode",
        "python run_simulation.py --visualization final --case 1 --max-time 45",
        wait_time=12
    )
    
    print(f"\n{'='*60}")
    print("All visualization tests completed!")
    print("Check the output above for any issues or errors.")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
