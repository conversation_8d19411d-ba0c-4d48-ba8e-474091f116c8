#!/usr/bin/env python3
"""
Test script for simplified obstacle features
测试简化障碍物特征的脚本
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from transport_time_predictor import TransportTimePredictorInterface
import time


def test_simplified_obstacle_features():
    """测试简化的障碍物特征"""
    print("=== Testing Simplified Obstacle Features ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 生成样本
        sample = predictor.data_generator.generate_sample()
        graph_data = sample['graph_data']
        
        print(f"Generated sample with simplified obstacle features:")
        print(f"  Node features shape: {graph_data.x.shape}")
        print(f"  Edge features shape: {graph_data.edge_attr.shape}")
        print(f"  Actual transport time: {sample['actual_time']:.4f}s")
        
        # 分析节点障碍物特征
        nodes_with_obstacles = 0
        for i in range(graph_data.x.shape[0]):
            node_features = graph_data.x[i]
            has_obstacle = node_features[2].item()
            if has_obstacle > 0:
                nodes_with_obstacles += 1
                
                # 检查障碍物信息
                obstacle_count = 0
                for j in range(10):
                    start_idx = 3 + j * 4
                    obs_start, obs_end, relevance, coverage = node_features[start_idx:start_idx+4]
                    if relevance > 0:
                        obstacle_count += 1
                        print(f"    Node {i} Obstacle {j+1}: relevance={relevance:.1f}, coverage={coverage:.1f}")
                
                if obstacle_count > 0:
                    print(f"  Node {i}: {obstacle_count} obstacles")
        
        # 分析边障碍物特征
        edges_with_obstacles = 0
        for i in range(graph_data.edge_attr.shape[0]):
            edge_features = graph_data.edge_attr[i]
            has_obstacle = edge_features[1].item()
            if has_obstacle > 0:
                edges_with_obstacles += 1
        
        print(f"  Nodes with obstacles: {nodes_with_obstacles}")
        print(f"  Edges with obstacles: {edges_with_obstacles}")
        
        return True
        
    except Exception as e:
        print(f"✗ Simplified obstacle features test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_simplified_actual_time_calculation():
    """测试简化的实际时间计算"""
    print("\n=== Testing Simplified Actual Time Calculation ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 生成多个样本并分析时间计算
        num_samples = 20
        base_times = []
        actual_times = []
        delays = []
        
        for _ in range(num_samples):
            sample = predictor.data_generator.generate_sample()
            
            # 计算基础时间
            distance = sample['distance']
            agv_speed = sample['agv_speed']
            base_time = distance / agv_speed
            actual_time = sample['actual_time']
            delay = actual_time - base_time
            
            base_times.append(base_time)
            actual_times.append(actual_time)
            delays.append(delay)
        
        print(f"Analysis of {num_samples} samples:")
        print(f"  Average base time: {np.mean(base_times):.4f}s")
        print(f"  Average actual time: {np.mean(actual_times):.4f}s")
        print(f"  Average delay: {np.mean(delays):.4f}s")
        print(f"  Max delay: {np.max(delays):.4f}s")
        print(f"  Samples with delay: {sum(1 for d in delays if d > 0.01)}/{num_samples}")
        
        # 验证时间计算的合理性
        if all(actual >= base for actual, base in zip(actual_times, base_times)):
            print("✓ All actual times >= base times (correct)")
        else:
            print("⚠ Some actual times < base times (potential issue)")
        
        if np.mean(delays) >= 0:
            print("✓ Average delay is non-negative (correct)")
        else:
            print("⚠ Average delay is negative (potential issue)")
        
        return True
        
    except Exception as e:
        print(f"✗ Simplified actual time calculation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_training_with_simplified_features():
    """测试使用简化特征的训练"""
    print("\n=== Testing Training with Simplified Features ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 使用小规模配置进行快速测试
        predictor.config.update({
            'num_epochs': 3,
            'batch_size': 8,
            'graph_hidden_dim': 32,
            'temporal_hidden_dim': 64
        })
        
        # 重新创建模型
        from transport_time_predictor import TransportTimePredictor
        predictor.model = TransportTimePredictor(predictor.config)
        predictor.model.to(predictor.device)
        
        print("Starting training with simplified obstacle features...")
        start_time = time.time()
        
        # 训练模型
        predictor.train_model(
            num_train_samples=100,
            num_val_samples=25
        )
        
        training_time = time.time() - start_time
        print(f"✓ Training completed in {training_time:.2f}s")
        
        # 测试预测
        pred_time = predictor.predict_transport_time('P1', 'P10', 1.0, 0.0)
        print(f"Sample prediction: {pred_time:.4f}s")
        
        return True
        
    except Exception as e:
        print(f"✗ Training with simplified features failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_performance_comparison():
    """测试性能对比"""
    print("\n=== Testing Performance Comparison ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 测试样本生成性能
        num_samples = 50
        start_time = time.time()
        
        samples = []
        for _ in range(num_samples):
            sample = predictor.data_generator.generate_sample()
            samples.append(sample)
        
        generation_time = time.time() - start_time
        
        print(f"Generated {num_samples} samples in {generation_time:.3f}s")
        print(f"Average time per sample: {generation_time/num_samples*1000:.2f}ms")
        
        # 测试预测性能
        start_time = time.time()
        
        for _ in range(num_samples):
            pred_time = predictor.predict_transport_time('P1', 'P10', 1.0, 0.0)
        
        prediction_time = time.time() - start_time
        
        print(f"Made {num_samples} predictions in {prediction_time:.3f}s")
        print(f"Average prediction time: {prediction_time/num_samples*1000:.2f}ms")
        
        # 分析样本质量
        actual_times = [s['actual_time'] for s in samples]
        distances = [s['distance'] for s in samples]
        
        print(f"\nSample quality analysis:")
        print(f"  Actual time range: {np.min(actual_times):.3f} - {np.max(actual_times):.3f}s")
        print(f"  Distance range: {np.min(distances):.3f} - {np.max(distances):.3f}m")
        print(f"  Valid samples: {len([t for t in actual_times if t > 0])}/{len(actual_times)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Performance comparison test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def visualize_simplified_features():
    """可视化简化特征"""
    print("\n=== Visualizing Simplified Features ===")
    
    try:
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 生成多个样本
        num_samples = 100
        samples = []
        for _ in range(num_samples):
            sample = predictor.data_generator.generate_sample()
            samples.append(sample)
        
        # 提取数据
        base_times = []
        actual_times = []
        delays = []
        distances = []
        speeds = []
        
        for sample in samples:
            distance = sample['distance']
            speed = sample['agv_speed']
            actual_time = sample['actual_time']
            base_time = distance / speed
            delay = actual_time - base_time
            
            base_times.append(base_time)
            actual_times.append(actual_time)
            delays.append(delay)
            distances.append(distance)
            speeds.append(speed)
        
        # 创建可视化
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 基础时间 vs 实际时间
        axes[0, 0].scatter(base_times, actual_times, alpha=0.6, s=30)
        axes[0, 0].plot([min(base_times), max(base_times)], [min(base_times), max(base_times)], 'r--', label='y=x')
        axes[0, 0].set_xlabel('Base Time (s)')
        axes[0, 0].set_ylabel('Actual Time (s)')
        axes[0, 0].set_title('Base Time vs Actual Time')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 延迟分布
        axes[0, 1].hist(delays, bins=20, alpha=0.7, color='orange')
        axes[0, 1].set_xlabel('Delay (s)')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].set_title('Delay Distribution')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 距离 vs 延迟
        axes[1, 0].scatter(distances, delays, alpha=0.6, s=30, c=speeds, cmap='viridis')
        cbar = plt.colorbar(axes[1, 0].collections[0], ax=axes[1, 0])
        cbar.set_label('AGV Speed (m/s)')
        axes[1, 0].set_xlabel('Distance (m)')
        axes[1, 0].set_ylabel('Delay (s)')
        axes[1, 0].set_title('Distance vs Delay (colored by speed)')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 速度分布
        unique_speeds = list(set(speeds))
        speed_counts = [speeds.count(speed) for speed in unique_speeds]
        axes[1, 1].bar(unique_speeds, speed_counts, alpha=0.7, color='green')
        axes[1, 1].set_xlabel('AGV Speed (m/s)')
        axes[1, 1].set_ylabel('Count')
        axes[1, 1].set_title('AGV Speed Distribution')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('simplified_obstacle_features.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print("✓ Simplified features visualization saved as 'simplified_obstacle_features.png'")
        return True
        
    except Exception as e:
        print(f"✗ Simplified features visualization failed: {e}")
        return False


def main():
    """主测试函数"""
    print("Simplified Obstacle Features Test")
    print("=" * 50)
    
    tests = [
        ("Simplified Obstacle Features", test_simplified_obstacle_features),
        ("Simplified Actual Time Calculation", test_simplified_actual_time_calculation),
        ("Training with Simplified Features", test_training_with_simplified_features),
        ("Performance Comparison", test_performance_comparison),
        ("Simplified Features Visualization", visualize_simplified_features),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ Test '{test_name}' crashed: {e}")
            results[test_name] = False
    
    # 总结结果
    print(f"\n{'='*50}")
    print("Test Results Summary:")
    print(f"{'='*50}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<40} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed!")
        print("\nKey simplifications implemented:")
        print("✓ Removed complex coverage calculations")
        print("✓ Simplified delay model (fixed percentage)")
        print("✓ Binary relevance for obstacle impact")
        print("✓ Streamlined feature encoding")
        print("✓ Maintained line-rectangle intersection detection")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
