#!/usr/bin/env python3
"""
Test script to verify obstacle generation consistency between 
transport_time_predictor.py and DO_generator.py
验证运输时间预测器和DO生成器的障碍物生成一致性
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from transport_time_predictor import TransportDataGenerator, create_sample_graph_structure
from DO_generator import DOGenerator, normal_random


def test_normal_random_consistency():
    """测试高斯随机数生成的一致性"""
    print("=== 测试高斯随机数生成一致性 ===")
    
    # 创建预测器数据生成器
    graph_structure = create_sample_graph_structure()
    predictor_generator = TransportDataGenerator(graph_structure)
    
    # 生成样本
    n_samples = 1000
    mean, std = 20.0, 5.0
    
    # 使用预测器的方法
    predictor_samples = [predictor_generator._normal_random(mean, std) for _ in range(n_samples)]
    
    # 使用DO_generator的方法
    do_samples = [normal_random(mean, std) for _ in range(n_samples)]
    
    # 统计分析
    predictor_mean = np.mean(predictor_samples)
    predictor_std = np.std(predictor_samples)
    do_mean = np.mean(do_samples)
    do_std = np.std(do_samples)
    
    print(f"目标参数: mean={mean}, std={std}")
    print(f"预测器生成: mean={predictor_mean:.2f}, std={predictor_std:.2f}")
    print(f"DO生成器: mean={do_mean:.2f}, std={do_std:.2f}")
    print(f"均值差异: {abs(predictor_mean - do_mean):.3f}")
    print(f"标准差差异: {abs(predictor_std - do_std):.3f}")
    
    # 绘制分布对比
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.hist(predictor_samples, bins=30, alpha=0.7, label='预测器', color='blue')
    plt.hist(do_samples, bins=30, alpha=0.7, label='DO生成器', color='red')
    plt.xlabel('值')
    plt.ylabel('频次')
    plt.title('高斯随机数分布对比')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 2, 2)
    plt.scatter(predictor_samples[:100], do_samples[:100], alpha=0.6)
    plt.xlabel('预测器样本')
    plt.ylabel('DO生成器样本')
    plt.title('样本相关性')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('normal_random_comparison.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print()


def test_obstacle_generation_format():
    """测试障碍物生成格式的一致性"""
    print("=== 测试障碍物生成格式一致性 ===")
    
    # 创建预测器数据生成器
    graph_structure = create_sample_graph_structure()
    predictor_generator = TransportDataGenerator(graph_structure)
    
    # 创建DO生成器配置
    do_params = {
        'total_obstacles': 5,
        'obstacle_types': {
            'type1': {
                'ratio': 1.0,
                'center': {
                    'x': {'min': 0, 'max': 20},
                    'y': {'min': 0, 'max': 20}
                },
                'size': {
                    'width': {'mean': 1.0, 'std': 0.2},
                    'height': {'mean': 1.0, 'std': 0.2}
                },
                'time_interval': {'mean': 20.0, 'std': 5.0},
                'duration': {'mean': 30.0, 'std': 5.0}
            }
        }
    }
    
    do_generator = DOGenerator(do_params)
    
    # 生成障碍物
    predictor_obstacles = predictor_generator.generate_obstacles(5, 200.0)
    do_obstacles = do_generator.generate_obstacles()
    
    print(f"预测器生成障碍物数量: {len(predictor_obstacles)}")
    print(f"DO生成器生成障碍物数量: {len(do_obstacles)}")
    
    # 分析预测器障碍物格式
    print("\n预测器障碍物格式:")
    for i, obs in enumerate(predictor_obstacles[:3]):
        center_x, center_y, width, height, insert_time, duration = obs
        print(f"  障碍物{i+1}: center=({center_x:.2f}, {center_y:.2f}), "
              f"size=({width:.2f}x{height:.2f}), time={insert_time:.2f}+{duration:.2f}")
    
    # 分析DO生成器障碍物格式
    print("\nDO生成器障碍物格式:")
    for i, obs in enumerate(do_obstacles[:3]):
        print(f"  障碍物{i+1}: center=({obs.center_x:.2f}, {obs.center_y:.2f}), "
              f"size=({obs.width:.2f}x{obs.height:.2f}), time={obs.insert_time:.2f}+{obs.duration:.2f}")
    
    # 统计分析
    predictor_stats = analyze_obstacle_stats(predictor_obstacles, "预测器")
    do_stats = analyze_obstacle_stats_do(do_obstacles, "DO生成器")
    
    # 绘制对比图
    plot_obstacle_comparison(predictor_stats, do_stats)
    
    print()


def analyze_obstacle_stats(obstacles, name):
    """分析预测器障碍物统计信息"""
    if not obstacles:
        return {}
    
    centers_x = [obs[0] for obs in obstacles]
    centers_y = [obs[1] for obs in obstacles]
    widths = [obs[2] for obs in obstacles]
    heights = [obs[3] for obs in obstacles]
    insert_times = [obs[4] for obs in obstacles]
    durations = [obs[5] for obs in obstacles]
    
    stats = {
        'center_x': {'mean': np.mean(centers_x), 'std': np.std(centers_x)},
        'center_y': {'mean': np.mean(centers_y), 'std': np.std(centers_y)},
        'width': {'mean': np.mean(widths), 'std': np.std(widths)},
        'height': {'mean': np.mean(heights), 'std': np.std(heights)},
        'insert_time': {'mean': np.mean(insert_times), 'std': np.std(insert_times)},
        'duration': {'mean': np.mean(durations), 'std': np.std(durations)}
    }
    
    print(f"\n{name}统计信息:")
    for key, value in stats.items():
        print(f"  {key}: mean={value['mean']:.2f}, std={value['std']:.2f}")
    
    return stats


def analyze_obstacle_stats_do(obstacles, name):
    """分析DO生成器障碍物统计信息"""
    if not obstacles:
        return {}
    
    centers_x = [obs.center_x for obs in obstacles]
    centers_y = [obs.center_y for obs in obstacles]
    widths = [obs.width for obs in obstacles]
    heights = [obs.height for obs in obstacles]
    insert_times = [obs.insert_time for obs in obstacles]
    durations = [obs.duration for obs in obstacles]
    
    stats = {
        'center_x': {'mean': np.mean(centers_x), 'std': np.std(centers_x)},
        'center_y': {'mean': np.mean(centers_y), 'std': np.std(centers_y)},
        'width': {'mean': np.mean(widths), 'std': np.std(widths)},
        'height': {'mean': np.mean(heights), 'std': np.std(heights)},
        'insert_time': {'mean': np.mean(insert_times), 'std': np.std(insert_times)},
        'duration': {'mean': np.mean(durations), 'std': np.std(durations)}
    }
    
    print(f"\n{name}统计信息:")
    for key, value in stats.items():
        print(f"  {key}: mean={value['mean']:.2f}, std={value['std']:.2f}")
    
    return stats


def plot_obstacle_comparison(predictor_stats, do_stats):
    """绘制障碍物统计对比图"""
    metrics = ['center_x', 'center_y', 'width', 'height', 'insert_time', 'duration']
    predictor_means = [predictor_stats[m]['mean'] for m in metrics]
    do_means = [do_stats[m]['mean'] for m in metrics]
    predictor_stds = [predictor_stats[m]['std'] for m in metrics]
    do_stds = [do_stats[m]['std'] for m in metrics]
    
    x = np.arange(len(metrics))
    width = 0.35
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 均值对比
    ax1.bar(x - width/2, predictor_means, width, label='预测器', alpha=0.8)
    ax1.bar(x + width/2, do_means, width, label='DO生成器', alpha=0.8)
    ax1.set_xlabel('指标')
    ax1.set_ylabel('均值')
    ax1.set_title('障碍物参数均值对比')
    ax1.set_xticks(x)
    ax1.set_xticklabels(metrics, rotation=45)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 标准差对比
    ax2.bar(x - width/2, predictor_stds, width, label='预测器', alpha=0.8)
    ax2.bar(x + width/2, do_stds, width, label='DO生成器', alpha=0.8)
    ax2.set_xlabel('指标')
    ax2.set_ylabel('标准差')
    ax2.set_title('障碍物参数标准差对比')
    ax2.set_xticks(x)
    ax2.set_xticklabels(metrics, rotation=45)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('obstacle_stats_comparison.png', dpi=150, bbox_inches='tight')
    plt.show()


def test_obstacle_data_structure():
    """测试障碍物数据结构的兼容性"""
    print("=== 测试障碍物数据结构兼容性 ===")
    
    # 创建预测器数据生成器
    graph_structure = create_sample_graph_structure()
    predictor_generator = TransportDataGenerator(graph_structure)
    
    # 生成障碍物
    obstacles = predictor_generator.generate_obstacles(3, 100.0)
    
    print("生成的障碍物数据结构:")
    for i, obs in enumerate(obstacles):
        center_x, center_y, width, height, insert_time, duration = obs
        
        # 计算边界（与DO_generator.py一致）
        top_left = (center_x - width / 2, center_y - height / 2)
        bottom_right = (center_x + width / 2, center_y + height / 2)
        
        print(f"障碍物 {i+1}:")
        print(f"  中心坐标: ({center_x:.2f}, {center_y:.2f})")
        print(f"  尺寸: {width:.2f} x {height:.2f}")
        print(f"  边界: {top_left} -> {bottom_right}")
        print(f"  时间: {insert_time:.2f}s + {duration:.2f}s")
        print()
    
    # 测试图数据创建
    try:
        graph_data = predictor_generator.create_graph_data(obstacles)
        print("✓ 图数据创建成功")
        print(f"  节点特征形状: {graph_data.x.shape}")
        print(f"  边索引形状: {graph_data.edge_index.shape}")
        print(f"  边特征形状: {graph_data.edge_attr.shape}")
    except Exception as e:
        print(f"✗ 图数据创建失败: {e}")
    
    print()


def main():
    """主测试函数"""
    print("障碍物生成一致性测试")
    print("=" * 50)
    
    try:
        # 测试高斯随机数生成
        test_normal_random_consistency()
        
        # 测试障碍物生成格式
        test_obstacle_generation_format()
        
        # 测试数据结构兼容性
        test_obstacle_data_structure()
        
        print("=" * 50)
        print("测试完成！")
        print()
        print("生成的文件:")
        print("- normal_random_comparison.png: 高斯随机数分布对比")
        print("- obstacle_stats_comparison.png: 障碍物统计对比")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
