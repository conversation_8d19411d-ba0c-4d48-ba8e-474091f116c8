"""
Transport Time Predictor Configuration
运输时间预测器配置文件
"""

# Model Configuration
MODEL_CONFIG = {
    # Graph Neural Network Configuration
    'node_features': 23,  # Node feature dimension [x, y, has_obstacle] + 10 * [obstacle_start, obstacle_end]
    'edge_features': 22,  # Edge feature dimension [distance, has_obstacle_on_path] + 10 * [obstacle_start, obstacle_end]
    'graph_hidden_dim': 64,  # Graph hidden layer dimension
    'graph_layers': 3,  # Number of graph neural network layers

    # LSTM Configuration
    'temporal_hidden_dim': 128,  # LSTM hidden layer dimension
    'temporal_layers': 2,  # Number of LSTM layers
    'obstacle_features': 60,  # Obstacle feature dimension (10 obstacles * 6 features)

    # Training Configuration
    'learning_rate': 0.001,
    'batch_size': 32,
    'num_epochs': 100,
    'weight_decay': 1e-5,
    'dropout_rate': 0.1,

    # Data Generation Configuration
    'max_obstacles_per_sample': 5,
    'time_horizon': 200.0,  # Time horizon
    'agv_speed_range': (0.5, 2.0),  # AGV speed range
    'obstacle_duration_range': (5.0, 30.0),  # Obstacle duration range
}

# Network Architecture Parameters
NETWORK_PARAMS = {
    # GNN Parameters
    'gnn_type': 'GCN',  # Options: 'GCN', 'GAT', 'GraphSAGE'
    'attention_heads': 4,  # Number of attention heads for GAT
    'gnn_activation': 'relu',
    'gnn_dropout': 0.1,

    # LSTM Parameters
    'lstm_bidirectional': False,
    'lstm_dropout': 0.1,

    # Prediction Head Parameters
    'predictor_layers': [128, 64, 1],
    'predictor_activation': 'relu',
    'predictor_dropout': 0.1,
}

# Training Strategy
TRAINING_CONFIG = {
    'optimizer': 'adam',  # Options: 'adam', 'sgd', 'adamw'
    'scheduler': 'reduce_on_plateau',  # Options: 'reduce_on_plateau', 'cosine', 'step'
    'early_stopping_patience': 20,
    'gradient_clip_norm': 1.0,

    # Data Split
    'train_ratio': 0.7,
    'val_ratio': 0.2,
    'test_ratio': 0.1,

    # Data Augmentation
    'noise_level': 0.01,  # Noise level added to features
    'augment_data': True,
}

# Evaluation Configuration
EVALUATION_CONFIG = {
    'metrics': ['mae', 'mse', 'rmse', 'mape'],
    'save_predictions': True,
    'plot_results': True,
    'confidence_intervals': True,
}

# File Path Configuration
PATH_CONFIG = {
    'model_save_dir': 'models/',
    'data_save_dir': 'data/',
    'log_dir': 'logs/',
    'result_dir': 'results/',
    'checkpoint_dir': 'checkpoints/',
}

# Device Configuration
DEVICE_CONFIG = {
    'use_gpu': True,
    'gpu_id': 0,
    'num_workers': 4,
    'pin_memory': True,
}

def get_config(config_name='default'):
    """Get configuration"""
    configs = {
        'default': {
            **MODEL_CONFIG,
            **NETWORK_PARAMS,
            **TRAINING_CONFIG,
            **EVALUATION_CONFIG,
            **PATH_CONFIG,
            **DEVICE_CONFIG
        },
        
        'fast_training': {
            **MODEL_CONFIG,
            **NETWORK_PARAMS,
            **TRAINING_CONFIG,
            **EVALUATION_CONFIG,
            **PATH_CONFIG,
            **DEVICE_CONFIG,
            # Fast training modifications
            'num_epochs': 20,
            'batch_size': 64,
            'graph_hidden_dim': 32,
            'temporal_hidden_dim': 64,
        },

        'high_accuracy': {
            **MODEL_CONFIG,
            **NETWORK_PARAMS,
            **TRAINING_CONFIG,
            **EVALUATION_CONFIG,
            **PATH_CONFIG,
            **DEVICE_CONFIG,
            # High accuracy modifications
            'graph_hidden_dim': 128,
            'temporal_hidden_dim': 256,
            'graph_layers': 4,
            'temporal_layers': 3,
            'learning_rate': 0.0005,
            'num_epochs': 200,
        },

        'debug': {
            **MODEL_CONFIG,
            **NETWORK_PARAMS,
            **TRAINING_CONFIG,
            **EVALUATION_CONFIG,
            **PATH_CONFIG,
            **DEVICE_CONFIG,
            # Debug configuration
            'num_epochs': 5,
            'batch_size': 8,
            'graph_hidden_dim': 16,
            'temporal_hidden_dim': 32,
        }
    }
    
    return configs.get(config_name, configs['default'])

def print_config(config):
    """Print configuration information"""
    print("Model Configuration:")
    print("=" * 50)

    categories = {
        'Model Architecture': ['node_features', 'edge_features', 'graph_hidden_dim',
                              'graph_layers', 'temporal_hidden_dim', 'temporal_layers'],
        'Training': ['learning_rate', 'batch_size', 'num_epochs', 'optimizer'],
        'Data': ['max_obstacles_per_sample', 'time_horizon', 'agv_speed_range'],
        'Device': ['use_gpu', 'gpu_id', 'num_workers']
    }
    
    for category, keys in categories.items():
        print(f"\n{category}:")
        print("-" * 30)
        for key in keys:
            if key in config:
                print(f"  {key}: {config[key]}")

def validate_config(config):
    """Validate configuration validity"""
    required_keys = [
        'node_features', 'edge_features', 'graph_hidden_dim', 'graph_layers',
        'temporal_hidden_dim', 'temporal_layers', 'learning_rate', 'batch_size',
        'num_epochs'
    ]
    
    missing_keys = [key for key in required_keys if key not in config]
    if missing_keys:
        raise ValueError(f"Missing required config keys: {missing_keys}")
    
    # 验证数值范围
    if config['learning_rate'] <= 0 or config['learning_rate'] > 1:
        raise ValueError("Learning rate must be in (0, 1]")
    
    if config['batch_size'] <= 0:
        raise ValueError("Batch size must be positive")
    
    if config['num_epochs'] <= 0:
        raise ValueError("Number of epochs must be positive")
    
    print("Configuration validation passed!")

# Predefined Experiment Configurations
EXPERIMENT_CONFIGS = {
    'baseline': {
        'description': 'Baseline GNN+LSTM model',
        'config': get_config('default'),
        'train_samples': 10000,
        'val_samples': 2000,
        'test_samples': 1000,
    },
    
    'ablation_no_gnn': {
        'description': 'LSTM only (no GNN)',
        'config': {
            **get_config('default'),
            'graph_layers': 0,  # 禁用GNN
        },
        'train_samples': 10000,
        'val_samples': 2000,
        'test_samples': 1000,
    },
    
    'ablation_no_lstm': {
        'description': 'GNN only (no LSTM)',
        'config': {
            **get_config('default'),
            'temporal_layers': 0,  # 禁用LSTM
        },
        'train_samples': 10000,
        'val_samples': 2000,
        'test_samples': 1000,
    },
    
    'large_scale': {
        'description': 'Large scale experiment',
        'config': get_config('high_accuracy'),
        'train_samples': 50000,
        'val_samples': 10000,
        'test_samples': 5000,
    },
    
    'quick_test': {
        'description': 'Quick test for debugging',
        'config': get_config('debug'),
        'train_samples': 100,
        'val_samples': 50,
        'test_samples': 50,
    }
}

def get_experiment_config(experiment_name):
    """Get experiment configuration"""
    if experiment_name not in EXPERIMENT_CONFIGS:
        available = list(EXPERIMENT_CONFIGS.keys())
        raise ValueError(f"Unknown experiment: {experiment_name}. Available: {available}")
    
    return EXPERIMENT_CONFIGS[experiment_name]

def list_experiments():
    """List all available experiment configurations"""
    print("Available Experiments:")
    print("=" * 50)
    for name, config in EXPERIMENT_CONFIGS.items():
        print(f"{name}: {config['description']}")
        print(f"  Train samples: {config['train_samples']}")
        print(f"  Val samples: {config['val_samples']}")
        print(f"  Test samples: {config['test_samples']}")
        print()

if __name__ == "__main__":
    # 演示配置使用
    print("Transport Time Predictor Configuration Demo")
    print("=" * 50)
    
    # 显示默认配置
    config = get_config('default')
    print_config(config)
    
    # 验证配置
    validate_config(config)
    
    # 显示可用实验
    print("\n")
    list_experiments()
