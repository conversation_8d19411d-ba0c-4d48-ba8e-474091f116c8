import random
import math
from dynamic_obstacle import DynamicObstacle

def normal_random(mean, std):
    """生成满足高斯分布的随机数

    Args:
        mean: 平均值
        std: 标准差

    Returns:
        float: 随机数
    """
    # Box-Muller变换
    u1 = random.random()
    u2 = random.random()
    z0 = math.sqrt(-2.0 * math.log(u1)) * math.cos(2.0 * math.pi * u2)
    return mean + z0 * std

class DOGenerator:
    def __init__(self, dynamic_obstacle_params):
        # 动态障碍生成参数配置表
        self.params = dynamic_obstacle_params
        # 障碍物列表
        self.obstacle_list = []
        # 总障碍物数量
        self.total_obstacles = self.params.get('total_obstacles', 20)
        # 障碍物类型配置
        self.obstacle_types = self.params.get('obstacle_types', {})
        # 当前时间（模拟相对时间，从0开始）
        self.current_time = 0.0
        # 随机数生成器
        self.random = random.Random()

    def generate_obstacles(self):
        """根据配置生成动态障碍物列表"""
        # 清空障碍物列表
        self.obstacle_list = []

        # 计算每种障碍物类型的数量
        obstacle_counts = {}
        remaining_obstacles = self.total_obstacles

        # 先分配有比例的障碍物类型
        for obstacle_type, config in self.obstacle_types.items():
            ratio = config.get('ratio', 0)
            count = int(self.total_obstacles * ratio)
            obstacle_counts[obstacle_type] = count
            remaining_obstacles -= count

        # 如果还有剩余障碍物，分配给第一个障碍物类型
        if remaining_obstacles > 0 and self.obstacle_types:
            first_type = list(self.obstacle_types.keys())[0]
            obstacle_counts[first_type] += remaining_obstacles

        # 生成每种类型的障碍物
        all_obstacles = []
        for obstacle_type, count in obstacle_counts.items():
            if obstacle_type in self.obstacle_types and count > 0:
                type_obstacles = self._generate_obstacles_by_type(obstacle_type, count)
                all_obstacles.extend(type_obstacles)

        # 按插入时间排序
        all_obstacles.sort(key=lambda x: x['insert_time'])

        # 创建障碍物对象
        for obstacle_data in all_obstacles:
            # 计算障碍物的左上和右下坐标
            center_x = obstacle_data['center_x']
            center_y = obstacle_data['center_y']
            width = obstacle_data['width']
            height = obstacle_data['height']

            # 计算左上角坐标
            top_left = (center_x - width / 2, center_y - height / 2)
            # 计算右下角坐标
            bottom_right = (center_x + width / 2, center_y + height / 2)

            # 创建新的动态障碍物
            obstacle = DynamicObstacle(
                top_left=top_left,
                bottom_right=bottom_right,
                insert_time=obstacle_data['insert_time'],  # 相对时间（秒）
                duration=obstacle_data['duration'],  # 持续时间（秒）
                obstacle_type=obstacle_data['obstacle_type']
            )
            self.obstacle_list.append(obstacle)

        return self.obstacle_list

    def _generate_obstacles_by_type(self, obstacle_type, count):
        """根据障碍物类型生成指定数量的障碍物

        Args:
            obstacle_type: 障碍物类型
            count: 障碍物数量

        Returns:
            list: 障碍物数据列表
        """
        if obstacle_type not in self.obstacle_types:
            return []

        config = self.obstacle_types[obstacle_type]
        obstacles = []

        # 当前时间点
        current_time = self.current_time

        for _ in range(count):
            # 生成障碍物中心坐标
            center_config = config.get('center', {})
            x_config = center_config.get('x', {'min': 0, 'max': 8})
            y_config = center_config.get('y', {'min': 0, 'max': 6})

            center_x = self.random.uniform(x_config.get('min', 0), x_config.get('max', 8))
            center_y = self.random.uniform(y_config.get('min', 0), y_config.get('max', 6))

            # 生成障碍物尺寸
            size_config = config.get('size', {})
            width_config = size_config.get('width', {'mean': 1.0, 'std': 0.2})
            height_config = size_config.get('height', {'mean': 1.0, 'std': 0.2})

            width = max(0.1, normal_random(
                width_config.get('mean', 1.0),
                width_config.get('std', 0.2)
            ))
            height = max(0.1, normal_random(
                height_config.get('mean', 1.0),
                height_config.get('std', 0.2)
            ))

            # 生成插入时间间隔
            time_interval_config = config.get('time_interval', {'mean': 20.0, 'std': 5.0})
            time_interval = max(1, normal_random(
                time_interval_config.get('mean', 20.0),
                time_interval_config.get('std', 5.0)
            ))

            # 计算插入时间（相对时间，秒）
            insert_time = current_time + time_interval
            current_time = insert_time

            # 生成障碍物持续时间
            duration_config = config.get('duration', {'mean': 30.0, 'std': 5.0})
            duration = max(1, normal_random(
                duration_config.get('mean', 30.0),
                duration_config.get('std', 5.0)
            ))

            # 创建障碍物数据
            obstacle_data = {
                'obstacle_type': obstacle_type,
                'center_x': center_x,
                'center_y': center_y,
                'width': width,
                'height': height,
                'insert_time': insert_time,
                'duration': duration
            }

            obstacles.append(obstacle_data)

        return obstacles

    def get_obstacle_list(self):
        """获取障碍物列表"""
        return self.obstacle_list

    def save_obstacle_list(self, file_path):
        """将障碍物列表存储到文件

        Args:
            file_path: 存储文件路径
        """
        # 构建障碍物数据，并增强可读性
        obstacle_data = []
        for i, obstacle in enumerate(self.obstacle_list):
            # 格式化时间字段，提高可读性
            # 相对时间（秒）
            insert_time = obstacle.insert_time
            # 持续时间（分钟）
            duration_minutes = obstacle.duration / 60 if isinstance(obstacle.duration, (int, float)) else 0.0

            # 构建障碍物数据字典
            obstacle_dict = {
                'id': f"obstacle_{i}",
                'obstacle_type': obstacle.obstacle_type,
                'center': {
                    'x': obstacle.center_x,
                    'y': obstacle.center_y
                },
                'size': {
                    'width': obstacle.width,
                    'height': obstacle.height
                },
                'coordinates': {
                    'top_left': obstacle.top_left,
                    'bottom_right': obstacle.bottom_right
                },
                'insert_time': insert_time,
                'duration_minutes': round(duration_minutes, 2)
            }
            obstacle_data.append(obstacle_dict)

        # 使用全局配置中的保存函数
        import config
        config.save_data_to_file(obstacle_data, file_path)