"""
全局配置文件
用于设置实验的casenumber，从ENV_CONFIG文件夹中读取对应的CASE<number>.py环境配置文件，
同时设置动态障碍与AGV任务生成的文件存储路径
"""
import os
import json
import importlib.util

# 获取当前脚本文件的目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

# 实验配置
EXPERIMENT = {
    'case_number': 1,  # 默认使用CASE1.py配置文件
    'random_seed': 42  # 随机数种子，用于保证实验可重复性
}

# 文件路径配置
PATHS = {
    'env_config_dir': os.path.join(SCRIPT_DIR, 'Env_config'),  # 环境配置文件目录
    'output_dir': os.path.join(SCRIPT_DIR, 'output'),  # 输出文件目录
    'task_output': os.path.join(SCRIPT_DIR, 'output', 'tasks'),  # AGV任务生成文件存储路径
    'obstacle_output': os.path.join(SCRIPT_DIR, 'output', 'obstacles')  # 动态障碍物文件存储路径
}

# 文件格式配置
FILE_FORMAT = {
    'indent': 4,  # JSON缩进空格数
    'sort_keys': True,  # 是否按键排序
    'ensure_ascii': False,  # 是否确保ASCII编码（False支持中文）
    'format': 'json'  # 文件格式，支持'json'和'yaml'（如果安装了PyYAML）
}

# 确保输出目录存在
def ensure_dirs_exist():
    """确保所有输出目录存在"""
    for path in [PATHS['output_dir'], PATHS['task_output'], PATHS['obstacle_output']]:
        if not os.path.exists(path):
            os.makedirs(path)

# 获取当前实验的环境配置文件路径
def get_env_config_path():
    """获取当前实验的环境配置文件路径"""
    case_file = f"CASE{EXPERIMENT['case_number']}.py"
    return os.path.join(PATHS['env_config_dir'], case_file)

# 加载环境配置
def load_env_config():
    """加载当前实验的环境配置"""
    config_path = get_env_config_path()
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"环境配置文件 {config_path} 不存在")

    # 使用importlib动态加载配置模块
    spec = importlib.util.spec_from_file_location("env_config", config_path)
    env_config = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(env_config)

    return env_config

# 获取文件扩展名
def get_file_extension():
    """根据配置的文件格式获取文件扩展名

    Returns:
        str: 文件扩展名
    """
    if FILE_FORMAT['format'].lower() == 'yaml':
        return '.yaml'
    return '.json'

# 获取任务输出文件路径
def get_task_output_path(suffix=None):
    """获取任务输出文件路径

    Args:
        suffix: 文件名后缀，默认为None

    Returns:
        str: 任务输出文件完整路径
    """
    filename = f"tasks_case{EXPERIMENT['case_number']}"
    if suffix:
        filename = f"{filename}_{suffix}"
    extension = get_file_extension()
    return os.path.join(PATHS['task_output'], f"{filename}{extension}")

# 获取障碍物输出文件路径
def get_obstacle_output_path(suffix=None):
    """获取障碍物输出文件路径

    Args:
        suffix: 文件名后缀，默认为None

    Returns:
        str: 障碍物输出文件完整路径
    """
    filename = f"obstacles_case{EXPERIMENT['case_number']}"
    if suffix:
        filename = f"{filename}_{suffix}"
    extension = get_file_extension()
    return os.path.join(PATHS['obstacle_output'], f"{filename}{extension}")

# 设置实验case编号
def set_case_number(case_number):
    """设置实验case编号

    Args:
        case_number: 实验case编号
    """
    EXPERIMENT['case_number'] = case_number

# 保存数据到文件
def save_data_to_file(data, file_path):
    """将数据保存到文件

    Args:
        data: 要保存的数据
        file_path: 文件路径
    """
    # 确保目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    # 根据文件格式保存数据
    if FILE_FORMAT['format'].lower() == 'yaml':
        try:
            import yaml
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(data, f, default_flow_style=False, allow_unicode=True, sort_keys=FILE_FORMAT['sort_keys'])
        except ImportError:
            print("PyYAML未安装，将使用JSON格式保存")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=FILE_FORMAT['indent'], ensure_ascii=FILE_FORMAT['ensure_ascii'], sort_keys=FILE_FORMAT['sort_keys'])
    else:
        # 默认使用JSON格式
        import json
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=FILE_FORMAT['indent'], ensure_ascii=FILE_FORMAT['ensure_ascii'], sort_keys=FILE_FORMAT['sort_keys'])

# 初始化配置
ensure_dirs_exist()
