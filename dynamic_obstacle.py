class DynamicObstacle:
    def __init__(self, top_left, bottom_right, insert_time, duration, obstacle_type=None):
        """
        初始化动态障碍物实例

        Args:
            top_left: 左上锚点坐标 (x, y)
            bottom_right: 右下锚点坐标 (x, y)
            insert_time: 插入时间
            duration: 持续时间
            obstacle_type: 障碍物类型
        """
        self.top_left = top_left
        self.bottom_right = bottom_right
        self.insert_time = insert_time
        self.duration = duration
        self.obstacle_type = obstacle_type

        # 计算障碍物尺寸
        self.width = self.bottom_right[0] - self.top_left[0]
        self.height = self.bottom_right[1] - self.top_left[1]

        # 计算障碍物中心坐标
        self.center_x = (self.top_left[0] + self.bottom_right[0]) / 2
        self.center_y = (self.top_left[1] + self.bottom_right[1]) / 2

    def is_active(self, current_time):
        """
        判断障碍物是否处于活动状态

        Args:
            current_time: 当前时间（相对时间，秒）

        Returns:
            bool: 是否处于活动状态
        """
        # 判断当前时间是否在障碍物的活动时间范围内
        if isinstance(self.insert_time, (int, float)) and isinstance(self.duration, (int, float)):
            # 如果是相对时间（秒）
            return current_time >= self.insert_time and current_time < self.insert_time + self.duration
        else:
            # 兼容旧版本的时间对象
            try:
                return current_time >= self.insert_time and current_time < self.insert_time + self.duration
            except:
                return False

    def get_area(self):
        """
        获取障碍物覆盖的区域范围

        Returns:
            list: 障碍物覆盖的所有坐标点列表
        """
        area = []
        for x in range(self.top_left[0], self.bottom_right[0] + 1):
            for y in range(self.top_left[1], self.bottom_right[1] + 1):
                area.append((x, y))
        return area

    def is_point_inside(self, point):
        """
        判断给定点是否在障碍物范围内

        Args:
            point: 待判断的点坐标 (x, y)

        Returns:
            bool: 是否在障碍物范围内
        """
        x, y = point
        return (self.top_left[0] <= x <= self.bottom_right[0] and
                self.top_left[1] <= y <= self.bottom_right[1])