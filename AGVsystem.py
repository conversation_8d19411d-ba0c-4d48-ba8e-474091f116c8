class AGVSystem:
    def __init__(self):
        # 地图：存放AGV任务点与路线
        self.map = {}
        
        # AGV池：存储所有AGV实例
        self.agv_pool = []
        
        # 任务池：存储所有任务实例
        self.task_pool = []
        
        # 动态障碍物集合：存储当前系统中的动态障碍物
        self.dynamic_obstacles = set()
        
        # 动态地图：用于实时更新的地图状态
        self.dynamic_map = None
    
    def update_system_status(self):
        """更新系统任务池、AGV池、动态障碍集合等参数"""
        # 更新任务池状态
        self._update_task_pool()
        
        # 更新AGV池状态
        self._update_agv_pool()
        
        # 更新动态障碍物集合
        self._update_dynamic_obstacles()
        
        # 更新动态地图
        self._update_dynamic_map()
    
    def get_agv_task_list(self):
        """获取AGV任务列表"""
        task_list = []
        for task in self.task_pool:
            if not task.is_assigned:
                task_list.append(task)
        return task_list
    
    def assign_agv_task(self, task_id, agv_id):
        """将任务分配给AGV，更新任务与AGV状态
        
        Args:
            task_id: 任务ID
            agv_id: AGV ID
        """
        # 查找任务和AGV
        task = next((t for t in self.task_pool if t.id == task_id), None)
        agv = next((a for a in self.agv_pool if a.id == agv_id), None)
        
        if task and agv:
            # 更新任务状态
            task.is_assigned = True
            task.assigned_agv = agv_id
            
            # 更新AGV状态
            agv.current_task = task_id
            agv.status = 'busy'
    
    def _update_task_pool(self):
        """更新任务池状态"""
        # 检查并更新每个任务的状态
        for task in self.task_pool:
            if task.is_completed:
                self.task_pool.remove(task)
    
    def _update_agv_pool(self):
        """更新AGV池状态"""
        # 更新每个AGV的状态
        for agv in self.agv_pool:
            agv.update_status()
    
    def _update_dynamic_obstacles(self):
        """更新动态障碍物集合"""
        # 获取所有AGV当前位置作为动态障碍物
        new_obstacles = set()
        for agv in self.agv_pool:
            if agv.current_position:
                new_obstacles.add(agv.current_position)
        self.dynamic_obstacles = new_obstacles
    
    def _update_dynamic_map(self):
        """更新动态地图"""
        # 基于静态地图和动态障碍物更新动态地图
        self.dynamic_map = self.map.copy()
        for obstacle in self.dynamic_obstacles:
            self._add_obstacle_to_map(obstacle)
    
    def _add_obstacle_to_map(self, obstacle_position):
        """将障碍物添加到动态地图中
        
        Args:
            obstacle_position: 障碍物位置
        """
        if obstacle_position in self.dynamic_map:
            self.dynamic_map[obstacle_position]['is_blocked'] = True