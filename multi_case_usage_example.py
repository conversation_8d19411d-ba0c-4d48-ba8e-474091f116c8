#!/usr/bin/env python3
"""
Multi-CASE Training and Simulation Usage Example
多CASE训练和仿真使用示例
"""

from transport_time_predictor import TransportTimePredictorInterface
from multi_case_config import get_experiment_config, list_available_configs
import time


def example_single_case_training():
    """示例1: 单CASE训练"""
    print("=" * 60)
    print("Example 1: Single CASE Training")
    print("=" * 60)
    
    # 创建预测器，使用CASE1
    predictor = TransportTimePredictorInterface(case_number=1)
    
    print(f"Loaded CASE{predictor.case_number} configuration:")
    case_info = predictor.get_case_info()
    for key, value in case_info.items():
        print(f"  {key}: {value}")
    
    # 单CASE训练
    print("\nTraining with single CASE...")
    start_time = time.time()
    
    predictor.train_model(
        num_train_samples=1000,
        num_val_samples=200
    )
    
    training_time = time.time() - start_time
    print(f"Training completed in {training_time:.2f}s")
    
    # 保存模型
    model_path = "single_case_model.pth"
    predictor.save_model(model_path)
    print(f"Model saved to {model_path}")
    
    # 测试预测
    pred_time = predictor.predict_transport_time(
        start_point='P1',
        end_point='P20',
        agv_speed=1.0,
        start_time=0.0
    )
    print(f"Sample prediction: P1->P20 = {pred_time:.2f}s")


def example_multi_case_training():
    """示例2: 多CASE训练"""
    print("\n" + "=" * 60)
    print("Example 2: Multi-CASE Training")
    print("=" * 60)
    
    # 创建预测器
    predictor = TransportTimePredictorInterface(case_number=1)
    
    # 多CASE训练（目前只有CASE1，可以扩展）
    case_numbers = [1]  # 可以添加更多CASE: [1, 2, 3]
    
    print(f"Training with multiple CASEs: {case_numbers}")
    start_time = time.time()
    
    predictor.train_multi_case_model(
        case_numbers=case_numbers,
        samples_per_case=800,
        val_ratio=0.2
    )
    
    training_time = time.time() - start_time
    print(f"Multi-CASE training completed in {training_time:.2f}s")
    
    # 保存模型
    model_path = "multi_case_model.pth"
    predictor.save_model(model_path)
    print(f"Multi-CASE model saved to {model_path}")


def example_simulation_with_case_switching():
    """示例3: 仿真中的CASE切换"""
    print("\n" + "=" * 60)
    print("Example 3: Simulation with CASE Switching")
    print("=" * 60)
    
    # 加载预训练模型
    try:
        predictor = TransportTimePredictorInterface(
            model_path="multi_case_model.pth",
            case_number=1
        )
        print("Loaded pre-trained multi-CASE model")
    except:
        # 如果没有预训练模型，创建新的
        predictor = TransportTimePredictorInterface(case_number=1)
        print("Created new predictor (no pre-trained model found)")
    
    # 模拟仿真场景
    simulation_scenarios = [
        {'case': 1, 'start': 'P1', 'end': 'P10', 'speed': 1.0, 'time': 0.0, 'description': 'Normal operation'},
        {'case': 1, 'start': 'P5', 'end': 'P15', 'speed': 1.5, 'time': 10.0, 'description': 'Fast AGV'},
        {'case': 1, 'start': 'P2', 'end': 'P18', 'speed': 1.0, 'time': 20.0, 'description': 'Long distance'},
        # 如果有更多CASE，可以添加：
        # {'case': 2, 'start': 'P3', 'end': 'P12', 'speed': 1.2, 'time': 30.0, 'description': 'Switch to CASE2'},
    ]
    
    print("\nSimulating transport scenarios:")
    for i, scenario in enumerate(simulation_scenarios):
        print(f"\nScenario {i+1}: {scenario['description']}")
        print(f"  CASE{scenario['case']}: {scenario['start']} -> {scenario['end']}")
        
        # 使用仿真预测接口（支持自动CASE切换）
        pred_time = predictor.predict_for_simulation(
            start_point=scenario['start'],
            end_point=scenario['end'],
            agv_speed=scenario['speed'],
            start_time=scenario['time'],
            current_case=scenario['case']
        )
        
        print(f"  Predicted time: {pred_time:.2f}s")


def example_case_comparison():
    """示例4: 不同CASE的预测对比"""
    print("\n" + "=" * 60)
    print("Example 4: CASE Comparison")
    print("=" * 60)
    
    # 创建预测器
    predictor = TransportTimePredictorInterface(case_number=1)
    
    # 测试场景
    test_scenario = {
        'start': 'P1',
        'end': 'P20',
        'speed': 1.0,
        'time': 0.0
    }
    
    # 比较不同CASE的预测结果
    case_numbers = [1]  # 可以扩展: [1, 2, 3]
    
    print(f"Comparing predictions across CASEs for {test_scenario['start']} -> {test_scenario['end']}:")
    
    for case_num in case_numbers:
        # 切换到指定CASE
        if case_num != predictor.case_number:
            predictor.switch_case(case_num)
        
        # 进行预测
        pred_time = predictor.predict_transport_time(
            start_point=test_scenario['start'],
            end_point=test_scenario['end'],
            agv_speed=test_scenario['speed'],
            start_time=test_scenario['time']
        )
        
        case_info = predictor.get_case_info()
        print(f"  CASE{case_num}: {pred_time:.2f}s (obstacles: {case_info['obstacles_count']})")


def example_training_strategies():
    """示例5: 不同训练策略"""
    print("\n" + "=" * 60)
    print("Example 5: Training Strategies")
    print("=" * 60)
    
    # 显示可用的配置
    print("Available training configurations:")
    list_available_configs()
    
    # 使用实验配置
    try:
        experiment_config = get_experiment_config('multi_case_experiment')
        print(f"\nUsing experiment configuration: {experiment_config['description']}")
        
        # 创建预测器
        predictor = TransportTimePredictorInterface(case_number=1)
        
        # 根据实验配置进行训练
        train_cases = experiment_config['multi_case_details']['train_cases']
        samples_per_case = experiment_config['multi_case_details']['samples_per_case']
        
        print(f"Training with CASEs: {train_cases}")
        print(f"Samples per CASE: {samples_per_case}")
        
        # 快速演示训练
        predictor.train_multi_case_model(
            case_numbers=train_cases,
            samples_per_case=min(samples_per_case, 500),  # 限制样本数用于演示
            val_ratio=0.2
        )
        
        print("Training strategy demonstration completed!")
        
    except Exception as e:
        print(f"Training strategy example failed: {e}")


def example_performance_evaluation():
    """示例6: 性能评估"""
    print("\n" + "=" * 60)
    print("Example 6: Performance Evaluation")
    print("=" * 60)
    
    # 创建预测器
    predictor = TransportTimePredictorInterface(case_number=1)
    
    # 快速训练用于演示
    print("Quick training for evaluation...")
    predictor.train_model(num_train_samples=500, num_val_samples=100)
    
    # 评估模型性能
    print("\nEvaluating model performance...")
    metrics = predictor.evaluate_model(test_samples=200)
    
    print("Performance metrics:")
    for metric, value in metrics.items():
        if isinstance(value, float):
            print(f"  {metric.upper()}: {value:.4f}")
        else:
            print(f"  {metric}: {value}")
    
    # 预测速度测试
    print("\nTesting prediction speed...")
    start_time = time.time()
    
    for _ in range(100):
        predictor.predict_transport_time('P1', 'P10', 1.0, 0.0)
    
    prediction_time = (time.time() - start_time) / 100
    print(f"Average prediction time: {prediction_time*1000:.2f}ms")


def main():
    """主函数"""
    print("Multi-CASE Transport Time Predictor Usage Examples")
    print("=" * 80)
    
    examples = [
        ("Single CASE Training", example_single_case_training),
        ("Multi-CASE Training", example_multi_case_training),
        ("Simulation with CASE Switching", example_simulation_with_case_switching),
        ("CASE Comparison", example_case_comparison),
        ("Training Strategies", example_training_strategies),
        ("Performance Evaluation", example_performance_evaluation),
    ]
    
    print("\nAvailable examples:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"  {i}. {name}")
    
    print("\nRunning all examples...")
    
    for name, example_func in examples:
        try:
            example_func()
        except Exception as e:
            print(f"\nExample '{name}' failed: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 80)
    print("All examples completed!")
    print("\nKey features demonstrated:")
    print("✓ Single and multi-CASE training")
    print("✓ Dynamic CASE switching during simulation")
    print("✓ Cross-CASE prediction comparison")
    print("✓ Configurable training strategies")
    print("✓ Performance evaluation and monitoring")
    print("✓ Real-time prediction capabilities")


if __name__ == "__main__":
    main()
