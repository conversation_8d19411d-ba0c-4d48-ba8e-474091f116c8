"""
Simplified Transport Time Predictor (without PyTorch dependencies)
简化版运输时间预测器（不依赖PyTorch）

This is a conceptual implementation showing the architecture and data flow
of the GNN+LSTM transport time predictor without requiring deep learning libraries.
"""

import numpy as np
import json
import random
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import time


@dataclass
class TransportSample:
    """运输时间预测样本"""
    graph_features: np.ndarray  # 图特征
    temporal_features: np.ndarray  # 时序特征
    obstacles: List[Tuple]  # 障碍物信息
    agv_speed: float  # AGV速度
    start_point: str  # 起始点ID
    end_point: str  # 目标点ID
    start_time: float  # AGV到达起始点的时间
    actual_time: float  # 实际运输时间


class SimpleGraphEncoder:
    """简化的图编码器"""
    
    def __init__(self, input_dim: int, hidden_dim: int):
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        # 简化的权重矩阵（实际中会用神经网络）
        self.weights = np.random.randn(input_dim, hidden_dim) * 0.1
        
    def encode(self, node_features: np.ndarray, adjacency_matrix: np.ndarray) -> np.ndarray:
        """
        编码图特征
        
        Args:
            node_features: 节点特征矩阵 [num_nodes, feature_dim]
            adjacency_matrix: 邻接矩阵 [num_nodes, num_nodes]
        
        Returns:
            编码后的节点嵌入 [num_nodes, hidden_dim]
        """
        # 简化的图卷积操作
        # 实际中会使用GCN/GAT等图神经网络
        
        # 特征变换
        transformed = np.dot(node_features, self.weights)
        
        # 邻居聚合（简化版图卷积）
        aggregated = np.dot(adjacency_matrix, transformed)
        
        # 激活函数
        output = np.tanh(aggregated)
        
        return output


class SimpleLSTMEncoder:
    """简化的LSTM编码器"""
    
    def __init__(self, input_dim: int, hidden_dim: int):
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        # 简化的LSTM权重（实际中会有更复杂的门控机制）
        self.weights = np.random.randn(input_dim + hidden_dim, hidden_dim) * 0.1
        
    def encode(self, sequence: np.ndarray) -> np.ndarray:
        """
        编码时序特征
        
        Args:
            sequence: 输入序列 [seq_len, feature_dim]
        
        Returns:
            编码后的特征 [hidden_dim]
        """
        hidden = np.zeros(self.hidden_dim)
        
        # 简化的LSTM前向传播
        for t in range(sequence.shape[0]):
            # 拼接输入和隐藏状态
            combined = np.concatenate([sequence[t], hidden])
            
            # 简化的LSTM计算
            hidden = np.tanh(np.dot(combined, self.weights))
        
        return hidden


class SimpleTransportPredictor:
    """简化的运输时间预测器"""
    
    def __init__(self, config: Dict):
        self.config = config
        
        # 初始化编码器
        self.graph_encoder = SimpleGraphEncoder(
            input_dim=config['node_features'],
            hidden_dim=config['graph_hidden_dim']
        )
        
        self.temporal_encoder = SimpleLSTMEncoder(
            input_dim=config['temporal_input_dim'],
            hidden_dim=config['temporal_hidden_dim']
        )
        
        # 预测器权重
        self.predictor_weights = np.random.randn(config['temporal_hidden_dim'], 1) * 0.1
        
    def predict(self, sample: TransportSample) -> float:
        """
        预测运输时间
        
        Args:
            sample: 输入样本
        
        Returns:
            预测的运输时间
        """
        # 图编码
        graph_embedding = self.graph_encoder.encode(
            sample.graph_features['node_features'],
            sample.graph_features['adjacency_matrix']
        )
        
        # 提取起始点和目标点的嵌入
        start_idx = sample.graph_features['point_to_idx'][sample.start_point]
        end_idx = sample.graph_features['point_to_idx'][sample.end_point]
        
        start_embedding = graph_embedding[start_idx]
        end_embedding = graph_embedding[end_idx]
        
        # 构建时序输入
        temporal_input = np.array([
            np.concatenate([
                start_embedding,
                end_embedding,
                sample.temporal_features
            ])
        ])
        
        # 时序编码
        temporal_output = self.temporal_encoder.encode(temporal_input)
        
        # 预测
        prediction = np.dot(temporal_output, self.predictor_weights)[0]
        
        return max(0.1, prediction)  # 确保预测时间为正


class TransportDataGenerator:
    """运输时间数据生成器"""
    
    def __init__(self, graph_structure: Dict):
        self.graph_structure = graph_structure
        self.task_points = graph_structure['task_points']
        self.channels = graph_structure['channels']
        
        # 构建图结构
        self._build_graph_matrices()
        
    def _build_graph_matrices(self):
        """构建图的邻接矩阵和距离矩阵"""
        num_points = len(self.task_points)
        self.adjacency_matrix = np.zeros((num_points, num_points))
        self.distance_matrix = np.full((num_points, num_points), np.inf)
        
        # 点ID到索引的映射
        self.point_to_idx = {point_id: idx for idx, point_id in enumerate(self.task_points.keys())}
        self.idx_to_point = {idx: point_id for point_id, idx in self.point_to_idx.items()}
        
        # 填充邻接矩阵和距离矩阵
        for (start, end), channel_info in self.channels.items():
            if start in self.task_points and end in self.task_points:
                start_idx = self.point_to_idx[start]
                end_idx = self.point_to_idx[end]
                
                # 计算欧几里得距离
                start_pos = self.task_points[start]
                end_pos = self.task_points[end]
                distance = np.sqrt((start_pos['x'] - end_pos['x'])**2 + 
                                 (start_pos['y'] - end_pos['y'])**2)
                
                self.adjacency_matrix[start_idx, end_idx] = 1
                self.adjacency_matrix[end_idx, start_idx] = 1
                self.distance_matrix[start_idx, end_idx] = distance
                self.distance_matrix[end_idx, start_idx] = distance
        
        # 对角线设为0
        np.fill_diagonal(self.distance_matrix, 0)
        
    def generate_obstacles(self, num_obstacles: int, time_horizon: float) -> List[Tuple]:
        """生成随机障碍物"""
        obstacles = []
        
        for _ in range(num_obstacles):
            # 随机选择节点
            point_id = random.choice(list(self.task_points.keys()))
            
            # 随机生成时间窗口
            t_start = random.uniform(0, time_horizon * 0.8)
            duration = random.uniform(5, 30)  # 5-30秒的障碍物持续时间
            t_end = t_start + duration
            
            obstacles.append((point_id, t_start, t_end))
        
        return obstacles
    
    def create_graph_features(self, obstacles: List[Tuple]) -> Dict:
        """创建图特征"""
        num_nodes = len(self.task_points)
        
        # 节点特征: [x, y, has_obstacle, obstacle_start, obstacle_end]
        node_features = []
        for point_id, point_info in self.task_points.items():
            # 基础位置特征
            features = [point_info['x'], point_info['y']]
            
            # 障碍物特征
            has_obstacle = 0
            obstacle_start = 0
            obstacle_end = 0
            
            for obs_point, t_start, t_end in obstacles:
                if obs_point == point_id:
                    has_obstacle = 1
                    obstacle_start = t_start
                    obstacle_end = t_end
                    break
            
            features.extend([has_obstacle, obstacle_start, obstacle_end])
            node_features.append(features)
        
        return {
            'node_features': np.array(node_features),
            'adjacency_matrix': self.adjacency_matrix,
            'point_to_idx': self.point_to_idx
        }
    
    def calculate_actual_time(self, start_point: str, end_point: str, agv_speed: float,
                            start_time: float, obstacles: List[Tuple]) -> float:
        """计算实际运输时间（考虑障碍物影响）"""
        start_idx = self.point_to_idx[start_point]
        end_idx = self.point_to_idx[end_point]
        
        base_distance = self.distance_matrix[start_idx, end_idx]
        base_time = base_distance / agv_speed
        
        # 计算障碍物延迟
        delay = 0.0
        for obs_point, t_start, t_end in obstacles:
            if obs_point in [start_point, end_point]:
                # 如果AGV到达时间与障碍物时间重叠
                arrival_time = start_time
                departure_time = start_time + base_time
                
                if not (departure_time < t_start or arrival_time > t_end):
                    # 有重叠，计算延迟
                    if arrival_time < t_start:
                        # AGV需要等待障碍物消失
                        delay += t_end - arrival_time
                    else:
                        # AGV到达时障碍物还在
                        delay += t_end - arrival_time
        
        return base_time + delay
    
    def generate_sample(self) -> TransportSample:
        """生成一个训练样本"""
        # 随机选择起始点和目标点
        point_ids = list(self.task_points.keys())
        start_point = random.choice(point_ids)
        end_point = random.choice([p for p in point_ids if p != start_point])
        
        # 随机生成AGV参数
        agv_speed = random.uniform(0.5, 2.0)  # 0.5-2.0 m/s
        start_time = random.uniform(0, 100)   # 0-100秒
        
        # 生成障碍物
        num_obstacles = random.randint(0, 5)
        obstacles = self.generate_obstacles(num_obstacles, 200)
        
        # 创建图特征
        graph_features = self.create_graph_features(obstacles)
        
        # 计算实际运输时间
        actual_time = self.calculate_actual_time(start_point, end_point, agv_speed, start_time, obstacles)
        
        # 构建时序特征
        start_idx = self.point_to_idx[start_point]
        end_idx = self.point_to_idx[end_point]
        distance = self.distance_matrix[start_idx, end_idx]
        
        temporal_features = np.array([
            agv_speed,
            start_time,
            distance,
            len(obstacles),
            sum(1 for obs_point, _, _ in obstacles if obs_point in [start_point, end_point])
        ])
        
        return TransportSample(
            graph_features=graph_features,
            temporal_features=temporal_features,
            obstacles=obstacles,
            agv_speed=agv_speed,
            start_point=start_point,
            end_point=end_point,
            start_time=start_time,
            actual_time=actual_time
        )


def create_sample_graph_structure() -> Dict:
    """创建示例图结构数据"""
    task_points = {}
    channels = {}
    
    # 创建5x5网格的任务点
    for i in range(5):
        for j in range(5):
            point_id = f"P{i*5 + j + 1}"
            task_points[point_id] = {'x': i * 2, 'y': j * 2}
    
    # 创建网格连接
    for i in range(5):
        for j in range(5):
            current_id = f"P{i*5 + j + 1}"
            
            # 水平连接
            if j < 4:
                next_id = f"P{i*5 + j + 2}"
                channels[(current_id, next_id)] = {'length': 2.0}
            
            # 垂直连接
            if i < 4:
                next_id = f"P{(i+1)*5 + j + 1}"
                channels[(current_id, next_id)] = {'length': 2.0}
    
    return {
        'task_points': task_points,
        'channels': channels
    }
