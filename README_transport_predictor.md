# Transport Time Predictor - GNN + LSTM

A deep learning-based transport time prediction module for AGV systems using Graph Neural Networks (GNN) and Long Short-Term Memory (LSTM) networks.

## Overview

This module predicts AGV transport times by considering:
- **Graph structure**: Task points and channels from CASE configuration files
- **Dynamic obstacles**: Real-time obstacles loaded from simulation output files
- **AGV parameters**: Speed and types defined in CASE configuration
- **Task patterns**: Task generation patterns based on CASE configuration
- **Temporal patterns**: Time-dependent obstacle and task patterns

### CASE Configuration Integration

The module automatically reads and uses CASE configuration files from `ENV_CONFIG/CASE{N}.py`, ensuring complete consistency with simulation parameters:

- **Task Points**: Exact same coordinates and IDs as simulation
- **Channels**: Same connectivity and distances as simulation
- **Task Generation**: Follows CASE-defined task type distributions and point selection rules
- **AGV Parameters**: Uses CASE-defined AGV types, speeds, and ratios
- **Obstacle Integration**: Reads obstacles from corresponding case output files

## Architecture

### Model Components

1. **Graph Neural Network (GNN)**
   - Encodes spatial relationships between task points
   - Processes obstacle information at each node
   - Uses Graph Convolutional Networks (GCN) with attention mechanisms

2. **Long Short-Term Memory (LSTM)**
   - Processes temporal sequences of transport features
   - Combines graph embeddings with AGV and obstacle information
   - Captures time-dependent patterns in transport behavior

3. **Prediction Head**
   - Multi-layer perceptron for final time prediction
   - Outputs estimated transport time in seconds

### Data Format

#### Input Sequence
```
(G_0, O_t, v, p_to, p_from, t_from, t_res)
```

Where:
- `G_0`: Original map topology (undirected graph)
- `O_t`: Node and edge obstacle information, format: (o, t_start, t_end)
- `v`: AGV speed
- `p_to`: Destination point
- `p_from`: Starting point
- `t_from`: AGV arrival time at p_from
- `t_res`: Transport time from p_from to p_to

#### Obstacle Data
The module reads obstacle data directly from `output/obstacles/obstacles_case{N}.json` files, maintaining consistency with the simulation system.

Obstacle format:
```json
{
    "id": "obstacle_0",
    "obstacle_type": "small_obstacle",
    "center": {"x": 4.62, "y": 3.66},
    "size": {"width": 0.70, "height": 0.85},
    "insert_time": 14.90,
    "duration_minutes": 0.26
}
```

## Files Structure

```
transport_time_predictor.py     # Main prediction module
transport_predictor_config.py   # Configuration management
multi_case_config.py            # Multi-CASE training configuration
test_transport_predictor.py     # Basic functionality tests
test_obstacle_loading.py        # Obstacle loading tests
test_case_integration.py        # CASE configuration integration tests
test_multi_case_training.py     # Multi-CASE training tests
test_obstacle_features.py       # Enhanced obstacle features tests
test_line_rectangle_intersection.py  # Line-rectangle intersection tests
test_intersection_simple.py     # Simplified intersection tests
test_no_coverage.py             # No coverage information tests
test_topology_astar.py          # Topology-based A* algorithm tests
multi_case_usage_example.py     # Multi-CASE usage examples
demo_transport_predictor.py     # Demonstration script
README_transport_predictor.md   # This documentation
```

## Usage

### Basic Usage

```python
from transport_time_predictor import TransportTimePredictorInterface

# Create predictor for CASE1 (automatically loads ENV_CONFIG/CASE1.py)
predictor = TransportTimePredictorInterface(case_number=1)

# Predict transport time using CASE1 configuration
predicted_time = predictor.predict_transport_time(
    start_point='P1',
    end_point='P20',
    agv_speed=1.0,
    start_time=0.0,
    obstacles=[]  # Will use obstacles from output/obstacles/obstacles_case1.json
)

print(f"Predicted transport time: {predicted_time:.2f} seconds")
```

### Multi-CASE Training

```python
# Multi-CASE training for better generalization
predictor = TransportTimePredictorInterface(case_number=1)

# Train with multiple CASEs
predictor.train_multi_case_model(
    case_numbers=[1, 2, 3],  # Train on multiple CASEs
    samples_per_case=5000,
    val_ratio=0.2
)

# Or use explicit multi-CASE training
predictor.train_model(
    num_train_samples=15000,
    num_val_samples=3000,
    train_case_numbers=[1, 2, 3],  # Training CASEs
    val_case_numbers=[1, 2, 3]     # Validation CASEs
)
```

### Simulation with CASE Switching

```python
# Create predictor with initial CASE
predictor = TransportTimePredictorInterface(case_number=1)

# Train on multiple CASEs for robustness
predictor.train_multi_case_model(case_numbers=[1, 2, 3])

# During simulation, switch CASEs dynamically
for simulation_step in simulation_steps:
    current_case = get_current_simulation_case()  # Your simulation logic

    predicted_time = predictor.predict_for_simulation(
        start_point='P1',
        end_point='P20',
        agv_speed=1.0,
        start_time=current_time,
        current_case=current_case  # Automatically switches if needed
    )
```

### Manual CASE Switching

```python
# Switch CASE configuration manually
predictor.switch_case(case_number=2)

# Get current CASE information
case_info = predictor.get_case_info()
print(f"Current CASE: {case_info['case_number']}")
print(f"Task points: {case_info['task_points_count']}")
print(f"Obstacles: {case_info['obstacles_count']}")
```

### Training a Model

```python
# Train with custom parameters
predictor.train_model(
    num_train_samples=10000,
    num_val_samples=2000
)

# Save trained model
predictor.save_model("my_transport_model.pth")

# Evaluate model performance
metrics = predictor.evaluate_model(test_samples=1000)
print(f"Model MAE: {metrics['mae']:.4f}")
```

### Configuration

```python
from transport_predictor_config import get_config

# Use different configurations
config = get_config('high_accuracy')  # or 'fast_training', 'debug'
predictor = TransportTimePredictorInterface(config=config)
```

## Key Features

### 1. Multi-CASE Training Support
- Train on multiple CASE configurations simultaneously
- Automatic data generation from different CASEs
- Balanced or weighted sampling strategies
- Cross-CASE validation and testing

### 2. Dynamic CASE Switching
- Runtime switching between CASE configurations
- Seamless integration with simulation environment
- Automatic obstacle and parameter updates
- Consistent prediction interface across CASEs

### 3. CASE Configuration Integration
- Automatically loads task points, channels, and parameters from ENV_CONFIG/CASE{N}.py
- Maintains exact consistency with simulation environment
- Supports task generation patterns defined in CASE configuration
- Uses AGV types and speeds from CASE configuration

### 4. Real Obstacle Integration
- Reads obstacle data directly from simulation output files
- Maintains consistency with DO_generator.py obstacle format
- Supports time-based obstacle filtering
- Integrates with CASE-specific obstacle files

### 5. Topology-based A* Pathfinding Algorithm
- **Original Map Structure**: Uses existing task points and channels without grid reconstruction
- **Graph-based Navigation**: Builds adjacency graph from CASE configuration channels
- **Obstacle-aware Routing**: Dynamically blocks edges intersected by active obstacles
- **Optimal Path Calculation**: A* algorithm finds shortest valid path through task point network
- **Real-time Performance**: Efficient execution with 0.24ms average pathfinding time

### 6. Advanced Path-Obstacle Intersection Detection
- **Geometric Precision**: Line-rectangle intersection detection for accurate path analysis
- **Comprehensive Coverage**: Detects obstacles affecting paths even when endpoints are outside obstacles
- **Multiple Detection Methods**: Endpoint overlap + line segment intersection with rectangle edges
- **Robust Algorithms**: Cross-product based line intersection with floating-point precision handling

### 7. Streamlined Obstacle Feature Encoding
- **Node Features**: Each node includes up to 10 obstacle time information entries (23D total)
- **Edge Features**: Each edge includes up to 10 obstacle time information entries (22D total)
- **Obstacle Details**: For each obstacle: start_time, end_time (simplified from complex coverage calculations)
- **Time-Based Ranking**: Obstacles ranked by start time for consistent temporal ordering
- **Path-Aware Detection**: Enhanced edge obstacle detection using line-rectangle intersection
- **Computational Efficiency**: Reduced feature dimensions for faster processing

### 8. Graph-based Spatial Modeling
- Encodes AGV system topology as graph structure from CASE configuration
- Processes spatial relationships between task points
- Handles dynamic obstacle placement on nodes and edges
- Uses exact coordinates and distances from CASE files
- Rich obstacle information embedded in both nodes and edges
- Precise geometric calculations for path-obstacle interactions
- A* pathfinding respects original map topology and channel connectivity

### 7. Temporal Pattern Learning
- LSTM networks capture time-dependent transport patterns
- Considers obstacle timing relative to transport schedule
- Learns from CASE-specific task and obstacle patterns
- Adapts to different CASE configurations automatically
- Enhanced temporal features with detailed obstacle timing

### 8. Flexible Configuration
- Multiple pre-configured model architectures
- Adjustable training parameters
- Support for different CASE experiment setups
- Automatic parameter adaptation based on CASE configuration
- Scalable obstacle feature encoding (up to 10 obstacles per node/edge)

## Model Performance

The model is designed to predict transport times with consideration for:
- **Base transport time**: Distance-based calculation
- **Obstacle delays**: Time penalties from dynamic obstacles
- **Route optimization**: Implicit learning of optimal paths
- **Temporal patterns**: Time-of-day and sequence effects

## Testing

Run the test suites to verify functionality:

```bash
# Basic functionality tests
python test_transport_predictor.py

# Obstacle loading tests
python test_obstacle_loading.py

# CASE configuration integration tests
python test_case_integration.py

# Multi-CASE training tests
python test_multi_case_training.py

# Enhanced obstacle features tests
python test_obstacle_features.py

# Line-rectangle intersection tests
python test_intersection_simple.py

# No coverage information tests
python test_no_coverage.py

# Topology-based A* algorithm tests
python test_topology_astar.py

# Multi-CASE usage examples
python multi_case_usage_example.py

# Full demonstration
python demo_transport_predictor.py
```

## Dependencies

- PyTorch >= 1.9.0
- PyTorch Geometric >= 2.0.0
- NumPy >= 1.20.0
- Matplotlib >= 3.3.0
- JSON (built-in)

## Configuration Options

### Model Architecture
- `node_features`: Node feature dimensions
- `edge_features`: Edge feature dimensions
- `graph_hidden_dim`: GNN hidden layer size
- `temporal_hidden_dim`: LSTM hidden layer size

### Training Parameters
- `learning_rate`: Optimizer learning rate
- `batch_size`: Training batch size
- `num_epochs`: Number of training epochs
- `early_stopping_patience`: Early stopping patience

### Data Parameters
- `case_number`: Which obstacle case file to use
- `time_horizon`: Maximum time range for obstacle consideration
- `agv_speed_range`: Range of AGV speeds for training

## Integration with DEAS-MARL

This module integrates seamlessly with the DEAS-MARL system by:

### Configuration Consistency
1. **CASE Files**: Directly reads ENV_CONFIG/CASE{N}.py files used by simulation
2. **Task Points**: Uses exact same coordinates and IDs as simulation environment
3. **Channels**: Maintains same connectivity and distance definitions
4. **AGV Parameters**: Follows same AGV types, speeds, and distributions

### Data Consistency
1. **Obstacle Data**: Reads from output/obstacles/obstacles_case{N}.json files
2. **Task Patterns**: Follows CASE-defined task generation patterns
3. **Time Units**: Uses same time units (seconds) as simulation
4. **Coordinate System**: Maintains same coordinate system as simulation

### Operational Integration
1. **Prediction Interface**: Provides transport time predictions for task allocation
2. **Real-time Compatibility**: Supports real-time obstacle and task data
3. **Multi-CASE Support**: Can switch between different CASE configurations
4. **Scalability**: Adapts automatically to different map sizes and complexities

## Future Enhancements

- Support for multi-AGV interference modeling
- Real-time obstacle prediction
- Reinforcement learning integration
- Advanced graph attention mechanisms
- Distributed training support

## License

This module is part of the DEAS-MARL project and follows the same licensing terms.
