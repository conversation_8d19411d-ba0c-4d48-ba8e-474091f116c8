"""
仿真实验主文件
实现测试流程：
1. 根据配置文件读取参数，初始化环境类，读入AGV任务序列与动态障碍区域序列
2. 进入主循环，初始化仿真时间t=0
   a. 如果AGV任务插入时间大于当前仿真时间，将该任务插入AGV任务池，更新任务序列
   b. 如果动态障碍插入时间大于当前仿真时间，将该动态障碍物插入动态障碍池，更新动态障碍序列
   c. 检查AGV池，当出现AGV路径因动态障碍导致当前路径不可行，执行路径与任务重调度算法
   d. 检查AGV池，如果存在空闲AGV，执行任务分配算法
   e. 执行系统状态更新算法，t=t+CT(仿真时间间隔)
   f. 将当前状态可视化
   g. 如果达到上限或者任务序列与任务池均为空：
      i. 计算该次仿真的整体表现
      ii. 存储该次仿真的表现指标等参数
      iii. 结束主循环
"""
import os
import json
import time
import config
from task import Task
from agv import AGV
from dynamic_obstacle import DynamicObstacle
from task_allocation import TaskAllocator
from path_planning import PathPlanner
from task_rescheduling import TaskRescheduler
from system_state import SystemState

# 尝试导入可视化模块
try:
    from visualization import Visualizer
    VISUALIZATION_AVAILABLE = True
except ImportError:
    print("警告: 可视化模块加载失败，将使用文本模式运行仿真")
    # 创建一个简单的可视化替代类
    class SimpleVisualizer:
        def __init__(self, mode="none"):
            self.mode = "none"

        def initialize(self, task_points, channels):
            pass

        def visualize(self, current_time, agv_list, task_pool, obstacle_pool, task_points, channels):
            print(f"\r时间: {current_time:.2f}s | AGV: {len(agv_list)} | 任务: {len(task_pool)} | 障碍物: {len(obstacle_pool)}", end="")

        def save_figure(self, filename):
            print(f"无法保存图形，因为可视化模块未加载")

        def close(self):
            pass

    Visualizer = SimpleVisualizer
    VISUALIZATION_AVAILABLE = False

class Simulation:
    def __init__(self, case_number=1, simulation_time_step=0.1, max_simulation_time=3600, visualization_speed=10.0):
        """
        初始化仿真实验

        Args:
            case_number: 实验case编号
            simulation_time_step: 仿真时间步长（秒）
            max_simulation_time: 最大仿真时间（秒）
            visualization_speed: 可视化速度倍数
        """
        # 设置实验case编号
        config.set_case_number(case_number)

        # 加载环境配置
        self.env_config = config.load_env_config()

        # 仿真参数
        self.simulation_time_step = simulation_time_step  # 仿真时间步长（秒）
        self.current_time = 0.0  # 当前仿真时间
        self.max_simulation_time = max_simulation_time  # 最大仿真时间（秒）
        self.visualization_speed = visualization_speed  # 可视化速度倍数

        # 任务点和通道
        self.task_points = self.env_config.task_points
        self.channels = self.env_config.channels

        # 任务和障碍物序列
        self.task_sequence = []  # 待插入的任务序列
        self.obstacle_sequence = []  # 待插入的障碍物序列

        # 任务池和障碍物池
        self.task_pool = []  # 已插入但未分配的任务
        self.obstacle_pool = []  # 当前活跃的障碍物

        # AGV列表
        self.agv_list = []

        # 算法模块
        self.task_allocator = TaskAllocator()
        self.path_planner = PathPlanner(self.task_points, self.channels)
        self.task_rescheduler = TaskRescheduler()

        # 系统状态
        self.system_state = SystemState()

        # 可视化
        self.visualizer = Visualizer(speed_factor=visualization_speed)

        # 性能指标
        self.performance_metrics = {
            'completed_tasks': 0,
            'total_tasks': 0,
            'avg_completion_time': 0,
            'avg_waiting_time': 0,
            'collision_count': 0,
            'deadlock_count': 0
        }

    def load_tasks(self, task_file_path=None):
        """
        加载任务序列

        Args:
            task_file_path: 任务文件路径，如果为None则使用默认路径
        """
        if task_file_path is None:
            task_file_path = config.get_task_output_path()

        try:
            with open(task_file_path, 'r', encoding='utf-8') as f:
                task_data = json.load(f)

            self.task_sequence = []
            for task_info in task_data:
                task = Task(
                    task_id=task_info['id'],
                    start_point=task_info['start_point'],
                    end_point=task_info['end_point'],
                    insert_time=task_info['insert_time'],
                    ddl=task_info['ddl'],
                    task_type=task_info.get('task_type')
                )
                self.task_sequence.append(task)

            # 按插入时间排序
            self.task_sequence.sort(key=lambda x: x.insert_time)
            self.performance_metrics['total_tasks'] = len(self.task_sequence)

            print(f"成功加载 {len(self.task_sequence)} 个任务")
        except Exception as e:
            print(f"加载任务失败: {e}")

    def load_obstacles(self, obstacle_file_path=None):
        """
        加载障碍物序列

        Args:
            obstacle_file_path: 障碍物文件路径，如果为None则使用默认路径
        """
        if obstacle_file_path is None:
            obstacle_file_path = config.get_obstacle_output_path()

        try:
            with open(obstacle_file_path, 'r', encoding='utf-8') as f:
                obstacle_data = json.load(f)

            self.obstacle_sequence = []
            for obstacle_info in obstacle_data:
                # 获取坐标
                if 'coordinates' in obstacle_info:
                    top_left = obstacle_info['coordinates']['top_left']
                    bottom_right = obstacle_info['coordinates']['bottom_right']
                else:
                    # 兼容旧格式
                    top_left = obstacle_info['top_left']
                    bottom_right = obstacle_info['bottom_right']

                # 获取插入时间和持续时间
                insert_time = obstacle_info['insert_time']
                if 'duration_minutes' in obstacle_info:
                    duration = obstacle_info['duration_minutes'] * 60  # 转换为秒
                else:
                    duration = obstacle_info['duration']

                obstacle = DynamicObstacle(
                    top_left=top_left,
                    bottom_right=bottom_right,
                    insert_time=insert_time,
                    duration=duration,
                    obstacle_type=obstacle_info.get('obstacle_type')
                )
                self.obstacle_sequence.append(obstacle)

            # 按插入时间排序
            self.obstacle_sequence.sort(key=lambda x: x.insert_time)

            print(f"成功加载 {len(self.obstacle_sequence)} 个障碍物")
        except Exception as e:
            print(f"加载障碍物失败: {e}")

    def load_agvs(self, agv_file_path=None):
        """
        加载AGV列表

        Args:
            agv_file_path: AGV文件路径，如果为None则使用默认路径
        """
        if agv_file_path is None:
            agv_file_path = config.get_task_output_path("agvs")

        print(f"尝试从 {agv_file_path} 加载AGV")

        try:
            with open(agv_file_path, 'r', encoding='utf-8') as f:
                agv_data = json.load(f)

            print(f"成功读取AGV数据，包含 {len(agv_data)} 个AGV")

            self.agv_list = []
            for i, agv_info in enumerate(agv_data):
                print(f"AGV {i+1}/{len(agv_data)}: ID={agv_info['id']}, 位置={agv_info['position']}, 类型={agv_info.get('agv_type')}, 速度={agv_info.get('speed', 1.0)}")

                agv = AGV(
                    agv_id=agv_info['id'],
                    agv_type=agv_info.get('agv_type'),
                    position=agv_info['position'],
                    speed=agv_info.get('speed', 1.0)
                )
                self.agv_list.append(agv)

            print(f"成功加载 {len(self.agv_list)} 个AGV")

            # 如果没有加载到AGV，创建默认AGV
            if not self.agv_list:
                print("未加载到AGV，创建默认AGV")
                for i in range(5):  # 创建5个AGV
                    # 使用任务点作为初始位置
                    position = list(self.task_points.keys())[i % len(self.task_points)]
                    agv = AGV(
                        agv_id=f"agv_{i}",
                        agv_type="standard_agv" if i < 3 else "fast_agv",
                        position=position,
                        speed=1.0 if i < 3 else 1.5
                    )
                    self.agv_list.append(agv)
                    print(f"创建默认AGV: ID={agv.id}, 位置={agv.position}, 类型={agv.agv_type}, 速度={agv.speed}")
        except Exception as e:
            print(f"加载AGV失败: {e}")
            print("创建默认AGV")
            for i in range(5):  # 创建5个AGV
                # 使用任务点作为初始位置
                position = list(self.task_points.keys())[i % len(self.task_points)]
                agv = AGV(
                    agv_id=f"agv_{i}",
                    agv_type="standard_agv" if i < 3 else "fast_agv",
                    position=position,
                    speed=1.0 if i < 3 else 1.5
                )
                self.agv_list.append(agv)
                print(f"创建默认AGV: ID={agv.id}, 位置={agv.position}, 类型={agv.agv_type}, 速度={agv.speed}")

    def update_task_pool(self):
        """更新任务池，将到达插入时间的任务加入任务池"""
        if not self.task_sequence:
            return

        # 将所有插入时间小于等于当前时间的任务加入任务池
        while self.task_sequence and self.task_sequence[0].insert_time <= self.current_time:
            task = self.task_sequence.pop(0)
            self.task_pool.append(task)
            print(f"时间 {self.current_time:.2f}s: 任务 {task.id} 加入任务池")

    def update_obstacle_pool(self):
        """更新障碍物池，将到达插入时间的障碍物加入障碍物池，移除过期的障碍物"""
        if not self.obstacle_sequence and not self.obstacle_pool:
            return

        # 将所有插入时间小于等于当前时间的障碍物加入障碍物池
        while self.obstacle_sequence and self.obstacle_sequence[0].insert_time <= self.current_time:
            obstacle = self.obstacle_sequence.pop(0)
            self.obstacle_pool.append(obstacle)
            print(f"时间 {self.current_time:.2f}s: 障碍物 {obstacle.obstacle_type} 加入障碍物池")

        # 移除过期的障碍物
        active_obstacles = []
        for obstacle in self.obstacle_pool:
            if obstacle.is_active(self.current_time):
                active_obstacles.append(obstacle)
            else:
                print(f"时间 {self.current_time:.2f}s: 障碍物 {obstacle.obstacle_type} 已过期")

        self.obstacle_pool = active_obstacles

    def check_path_conflicts(self):
        """检查AGV路径是否与障碍物冲突，如果冲突则重新规划路径"""
        for agv in self.agv_list:
            if agv.status != 'busy' or not agv.path:
                continue

            # 检查路径是否与障碍物冲突
            path_conflict = self.path_planner.check_path_conflict(agv.path, self.obstacle_pool, self.current_time)

            if path_conflict:
                print(f"时间 {self.current_time:.2f}s: AGV {agv.id} 路径与障碍物冲突，需要重新规划路径")

                # 执行路径与任务重调度算法
                self.task_rescheduler.reschedule(
                    agv=agv,
                    task_pool=self.task_pool,
                    agv_list=self.agv_list,
                    obstacle_pool=self.obstacle_pool,
                    path_planner=self.path_planner,
                    current_time=self.current_time
                )

    def allocate_tasks(self):
        """为空闲的AGV分配任务"""
        if not self.task_pool:
            return

        idle_agvs = [agv for agv in self.agv_list if agv.status == 'idle']
        if not idle_agvs:
            return

        # 执行任务分配算法
        allocations = self.task_allocator.allocate(
            idle_agvs=idle_agvs,
            task_pool=self.task_pool,
            obstacle_pool=self.obstacle_pool,
            path_planner=self.path_planner,
            current_time=self.current_time
        )

        # 处理分配结果
        for agv_id, task_id in allocations.items():
            agv = next((a for a in self.agv_list if a.id == agv_id), None)
            task = next((t for t in self.task_pool if t.id == task_id), None)

            if agv and task:
                # 从任务池中移除任务
                self.task_pool.remove(task)

                # 分配任务给AGV
                agv.assign_task(task)

                # 规划路径
                path = self.path_planner.plan_path(
                    start=agv.position,
                    end=task.start_point,
                    obstacle_pool=self.obstacle_pool,
                    current_time=self.current_time
                )
                agv.path = path

                print(f"时间 {self.current_time:.2f}s: 任务 {task.id} 分配给 AGV {agv.id}")

    def update_system_state(self):
        """更新系统状态，包括AGV位置、任务状态等"""
        # 更新AGV位置和状态
        for agv in self.agv_list:
            if agv.status == 'busy' and agv.path:
                # 计算AGV在当前时间步内可以移动的距离
                move_distance = agv.speed * self.simulation_time_step

                # 更新AGV位置
                new_position = self.path_planner.update_position(
                    agv=agv,
                    move_distance=move_distance,
                    current_time=self.current_time
                )

                # 如果AGV到达目标点
                if new_position == agv.target_position:
                    if agv.target_position == agv.current_task.start_point:
                        # AGV到达任务起点，开始前往终点
                        print(f"时间 {self.current_time:.2f}s: AGV {agv.id} 到达任务 {agv.current_task.id} 起点")

                        # 规划从起点到终点的路径
                        path = self.path_planner.plan_path(
                            start=agv.target_position,
                            end=agv.current_task.end_point,
                            obstacle_pool=self.obstacle_pool,
                            current_time=self.current_time
                        )
                        agv.path = path
                        agv.target_position = agv.current_task.end_point

                    elif agv.target_position == agv.current_task.end_point:
                        # AGV到达任务终点，完成任务
                        print(f"时间 {self.current_time:.2f}s: AGV {agv.id} 完成任务 {agv.current_task.id}")

                        # 更新性能指标
                        self.performance_metrics['completed_tasks'] += 1

                        # 完成任务
                        agv.complete_task()

        # 更新系统状态
        self.system_state.update(
            current_time=self.current_time,
            agv_list=self.agv_list,
            task_pool=self.task_pool,
            obstacle_pool=self.obstacle_pool
        )

    def visualize(self):
        """可视化当前系统状态"""
        self.visualizer.visualize(
            current_time=self.current_time,
            agv_list=self.agv_list,
            task_pool=self.task_pool,
            obstacle_pool=self.obstacle_pool,
            task_points=self.task_points,
            channels=self.channels
        )

    def calculate_performance(self):
        """计算性能指标"""
        if self.performance_metrics['completed_tasks'] > 0:
            # 计算平均完成时间和等待时间
            completion_times = []
            waiting_times = []

            for agv in self.agv_list:
                if hasattr(agv, 'completed_tasks'):
                    for task_info in agv.completed_tasks:
                        completion_time = task_info['completion_time'] - task_info['insert_time']
                        waiting_time = task_info['start_time'] - task_info['insert_time']

                        completion_times.append(completion_time)
                        waiting_times.append(waiting_time)

            if completion_times:
                self.performance_metrics['avg_completion_time'] = sum(completion_times) / len(completion_times)

            if waiting_times:
                self.performance_metrics['avg_waiting_time'] = sum(waiting_times) / len(waiting_times)

        # 计算完成率
        self.performance_metrics['completion_rate'] = (
            self.performance_metrics['completed_tasks'] / self.performance_metrics['total_tasks']
            if self.performance_metrics['total_tasks'] > 0 else 0
        )

        return self.performance_metrics

    def save_results(self, output_file=None):
        """保存仿真结果

        Args:
            output_file: 输出文件路径，如果为None则使用默认路径
        """
        if output_file is None:
            output_file = f"output/simulation_results_case{config.EXPERIMENT['case_number']}.json"

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # 计算性能指标
        performance = self.calculate_performance()

        # 构建结果数据
        results = {
            'case_number': config.EXPERIMENT['case_number'],
            'simulation_time': self.current_time,
            'performance_metrics': performance,
            'configuration': {
                'simulation_time_step': self.simulation_time_step,
                'max_simulation_time': self.max_simulation_time,
                'agv_count': len(self.agv_list),
                'total_tasks': self.performance_metrics['total_tasks'],
                'total_obstacles': len(self.obstacle_sequence) + len(self.obstacle_pool)
            }
        }

        # 保存结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=4, ensure_ascii=False)

        print(f"仿真结果已保存到: {output_file}")
        return results

    def run(self):
        """运行仿真实验"""
        print("开始仿真实验...")
        print(f"实验case编号: {config.EXPERIMENT['case_number']}")

        # 初始化仿真时间
        self.current_time = 0.0

        # 初始化可视化
        self.visualizer.initialize(
            task_points=self.task_points,
            channels=self.channels
        )

        # 主循环
        while True:
            # a. 更新任务池
            self.update_task_pool()

            # b. 更新障碍物池
            self.update_obstacle_pool()

            # c. 检查路径冲突
            self.check_path_conflicts()

            # d. 分配任务
            self.allocate_tasks()

            # e. 更新系统状态
            self.update_system_state()

            # f. 可视化
            self.visualize()

            # g. 检查终止条件
            if (
                self.current_time >= self.max_simulation_time or
                (not self.task_sequence and not self.task_pool and
                 all(agv.status == 'idle' for agv in self.agv_list))
            ):
                break

            # 更新仿真时间
            self.current_time += self.simulation_time_step

            # 在快速模式下，减少一些不必要的操作
            if self.visualizer.mode != "fast":
                # 控制仿真速度（可选）
                # time.sleep(0.01)
                pass

        # 计算性能指标
        performance = self.calculate_performance()
        print("\n仿真实验结束")
        print(f"总仿真时间: {self.current_time:.2f}s")
        print(f"完成任务数: {performance['completed_tasks']}/{performance['total_tasks']}")
        print(f"完成率: {performance['completion_rate']*100:.2f}%")
        print(f"平均完成时间: {performance['avg_completion_time']:.2f}s")
        print(f"平均等待时间: {performance['avg_waiting_time']:.2f}s")

        # 保存结果
        self.save_results()

        return performance

def main():
    """主函数"""
    # 设置实验case编号
    case_number = 1

    # 创建仿真实例
    simulation = Simulation(
        case_number=case_number,
        simulation_time_step=0.1,
        max_simulation_time=3600
    )

    # 加载任务、障碍物和AGV
    simulation.load_tasks()
    simulation.load_obstacles()
    simulation.load_agvs()

    # 运行仿真
    simulation.run()

if __name__ == "__main__":
    main()
