class Map:
    def __init__(self):
        # 任务点集合：存储所有任务点的位置和属性
        self.task_points = {}
        
        # 通道集合：存储所有通道的连接关系和属性
        self.channels = {}
        
        # 离散点运输距离表：存储任意两点间的运输距离
        self.distance_matrix = {}
    
    def initialize(self, config_file):
        """根据配置文件生成地图参数
        
        Args:
            config_file: 配置文件路径
        """
        # 读取配置文件并初始化地图参数
        self._read_task_points(config_file)
        self._read_channels(config_file)
        self._calculate_distances()
    
    def _read_task_points(self, config_file):
        """读取AGV离散任务点位置信息
        
        Args:
            config_file: 配置文件路径
        """
        # 导入配置文件中的任务点信息
        import importlib.util
        spec = importlib.util.spec_from_file_location("config", config_file)
        config = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config)
        
        # 将任务点信息写入self.task_points
        self.task_points = config.task_points.copy()
    
    def _read_channels(self, config_file):
        """读取通道信息
        
        Args:
            config_file: 配置文件路径
        """
        # 导入配置文件中的通道信息
        import importlib.util
        spec = importlib.util.spec_from_file_location("config", config_file)
        config = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config)
        
        # 将通道信息写入self.channels
        self.channels = config.channels.copy()
    
    def _calculate_distances(self):
        """计算任务点集合内两点间的运输距离"""
        # 初始化距离矩阵
        points = list(self.task_points.keys())
        n = len(points)
        dist = {(points[i], points[j]): float('inf') 
                for i in range(n) for j in range(n)}
        
        # 设置直接相连的通道距离
        for (p1, p2), channel in self.channels.items():
            dist[(p1, p2)] = channel['distance']
            dist[(p2, p1)] = channel['distance']  # 双向通道
        
        # 设置自身到自身的距离为0
        for p in points:
            dist[(p, p)] = 0
        
        # Floyd-Warshall算法计算最短路径
        for k in points:
            for i in points:
                for j in points:
                    if dist[(i, j)] > dist[(i, k)] + dist[(k, j)]:
                        dist[(i, j)] = dist[(i, k)] + dist[(k, j)]
        
        self.distance_matrix = dist
    
    def get_distance(self, point1, point2):
        """获取两点间的运输距离
        
        Args:
            point1: 起始点
            point2: 终点
            
        Returns:
            float: 两点间的运输距离
        """
        if (point1, point2) in self.distance_matrix:
            return self.distance_matrix[(point1, point2)]
        return float('inf')  # 如果两点间没有可行路径，返回无穷大
    
    def get_task_points(self):
        """获取所有任务点信息
        
        Returns:
            dict: 任务点信息字典
        """
        return self.task_points
    
    def get_channels(self):
        """获取所有通道信息
        
        Returns:
            dict: 通道信息字典
        """
        return self.channels