"""
系统状态更新模块
负责更新和维护系统的当前状态
"""

class SystemState:
    def __init__(self):
        """初始化系统状态"""
        # 当前时间
        self.current_time = 0.0
        
        # AGV状态
        self.agv_states = {}
        
        # 任务状态
        self.task_states = {}
        
        # 障碍物状态
        self.obstacle_states = {}
        
        # 性能指标
        self.performance_metrics = {
            'completed_tasks': 0,
            'total_tasks': 0,
            'avg_completion_time': 0,
            'avg_waiting_time': 0,
            'collision_count': 0,
            'deadlock_count': 0
        }
        
        # 历史状态
        self.history = []
    
    def update(self, current_time, agv_list, task_pool, obstacle_pool):
        """
        更新系统状态
        
        Args:
            current_time: 当前时间
            agv_list: AGV列表
            task_pool: 任务池
            obstacle_pool: 障碍物池
        """
        # 更新当前时间
        self.current_time = current_time
        
        # 更新AGV状态
        self.agv_states = {}
        for agv in agv_list:
            self.agv_states[agv.id] = {
                'position': agv.position,
                'status': agv.status,
                'current_task': agv.current_task.id if agv.current_task else None,
                'target_position': agv.target_position,
                'path': agv.path,
                'path_index': agv.path_index
            }
        
        # 更新任务状态
        self.task_states = {}
        for task in task_pool:
            self.task_states[task.id] = {
                'start_point': task.start_point,
                'end_point': task.end_point,
                'insert_time': task.insert_time,
                'ddl': task.ddl,
                'status': task.status,
                'assigned_agv': task.assigned_agv,
                'is_assigned': task.is_assigned,
                'is_completed': task.is_completed
            }
        
        # 更新障碍物状态
        self.obstacle_states = {}
        for i, obstacle in enumerate(obstacle_pool):
            self.obstacle_states[f"obstacle_{i}"] = {
                'top_left': obstacle.top_left,
                'bottom_right': obstacle.bottom_right,
                'insert_time': obstacle.insert_time,
                'duration': obstacle.duration,
                'is_active': obstacle.is_active(current_time)
            }
        
        # 保存当前状态到历史记录
        self.history.append({
            'time': current_time,
            'agv_states': self.agv_states.copy(),
            'task_states': self.task_states.copy(),
            'obstacle_states': self.obstacle_states.copy()
        })
    
    def get_current_state(self):
        """
        获取当前系统状态
        
        Returns:
            dict: 当前系统状态
        """
        return {
            'time': self.current_time,
            'agv_states': self.agv_states,
            'task_states': self.task_states,
            'obstacle_states': self.obstacle_states
        }
    
    def get_history(self, start_time=None, end_time=None):
        """
        获取历史状态
        
        Args:
            start_time: 起始时间，如果为None则从头开始
            end_time: 结束时间，如果为None则到最新状态
            
        Returns:
            list: 历史状态列表
        """
        if start_time is None and end_time is None:
            return self.history
        
        filtered_history = []
        for state in self.history:
            time = state['time']
            if (start_time is None or time >= start_time) and (end_time is None or time <= end_time):
                filtered_history.append(state)
        
        return filtered_history
    
    def calculate_performance_metrics(self):
        """
        计算性能指标
        
        Returns:
            dict: 性能指标
        """
        # 计算已完成任务数
        completed_tasks = sum(1 for task_state in self.task_states.values() if task_state['is_completed'])
        
        # 更新性能指标
        self.performance_metrics['completed_tasks'] = completed_tasks
        self.performance_metrics['total_tasks'] = len(self.task_states)
        
        # 计算完成率
        if self.performance_metrics['total_tasks'] > 0:
            self.performance_metrics['completion_rate'] = (
                self.performance_metrics['completed_tasks'] / self.performance_metrics['total_tasks']
            )
        else:
            self.performance_metrics['completion_rate'] = 0
        
        return self.performance_metrics
    
    def detect_collisions(self):
        """
        检测AGV之间的碰撞
        
        Returns:
            list: 碰撞事件列表
        """
        collisions = []
        
        # 获取所有AGV的位置
        agv_positions = {}
        for agv_id, agv_state in self.agv_states.items():
            agv_positions[agv_id] = agv_state['position']
        
        # 检测AGV之间的碰撞
        for agv_id1, pos1 in agv_positions.items():
            for agv_id2, pos2 in agv_positions.items():
                if agv_id1 != agv_id2 and pos1 == pos2:
                    collisions.append({
                        'time': self.current_time,
                        'agv1': agv_id1,
                        'agv2': agv_id2,
                        'position': pos1
                    })
        
        return collisions
    
    def detect_deadlocks(self):
        """
        检测系统中的死锁
        
        Returns:
            list: 死锁事件列表
        """
        # TODO: 实现死锁检测算法
        return []
